{"name": "ruoyi", "version": "3.6.6", "description": "若依管理系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@univerjs/preset-sheets-core": "^0.10.3", "@univerjs/presets": "^0.10.3", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "13.3.0", "axios": "1.9.0", "clipboard": "2.0.11", "echarts": "5.6.0", "element-plus": "2.10.7", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "lodash-es": "^4.17.21", "nprogress": "0.2.0", "pinia": "3.0.2", "splitpanes": "4.0.4", "uuid": "^11.1.0", "vue": "3.5.16", "vue-cropper": "1.1.1", "vue-router": "4.5.1", "vuedraggable": "4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.4", "sass-embedded": "1.89.1", "typescript": "^5.9.2", "unplugin-auto-import": "0.18.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "6.3.5", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}, "overrides": {"quill": "2.0.2"}}
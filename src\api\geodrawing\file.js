import request from '@/utils/request'

// 查询图纸文件列表
export function listFile(query) {
  return request({
    url: '/geodrawing/file/list',
    method: 'get',
    params: query
  })
}

// 查询图纸文件详细
export function getFile(id) {
  return request({
    url: '/geodrawing/file/' + id,
    method: 'get'
  })
}

// 新增图纸文件
export function addFile(data) {
  return request({
    url: '/geodrawing/file',
    method: 'post',
    data: data
  })
}

// 修改图纸文件
export function updateFile(data) {
  return request({
    url: '/geodrawing/file',
    method: 'put',
    data: data
  })
}

// 删除图纸文件
export function delFile(id) {
  return request({
    url: '/geodrawing/file/' + id,
    method: 'delete'
  })
}

// 修复检测结果
export function repairLogicVisionResult(id) {
  return request({
    url: '/geodrawing/file/repairLogicVisionResult/' + id,
    method: 'post'
  })
}

// 审查报告
export function investReport(layoutId, geoId, projectId) {
  return request({
    url: '/geodrawing/file/investReport/' + layoutId + '/' + geoId + '/' + projectId,
    method: 'post'
  })
}

// 上传图纸文件
export function uploadDrawingFile(data) {
  return request({
    url: '/geodrawing/file/uploadDrawingFile',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 查询检测结果
export function queryDetectionResult(id) {
  return request({
    url: '/geodrawing/file/queryDetectionResult/drawing/' + id,
    method: 'get',
  })
}

// 识别图纸文件
export function recognizeDrawingPages(data) {
  return request({
    url: '/geodrawing/file/recognizeDrawingPages',
    method: 'post',
    data
  })
}
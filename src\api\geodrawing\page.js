import request from '@/utils/request'

// 查询图纸页面列表
export function listPage(query) {
  return request({
    url: '/geodrawing/page/list',
    method: 'get',
    params: query
  })
}

// 查询图纸页面详细
export function getPage(id) {
  return request({
    url: '/geodrawing/page/' + id,
    method: 'get'
  })
}

// 新增图纸页面
export function addPage(data) {
  return request({
    url: '/geodrawing/page',
    method: 'post',
    data: data
  })
}

// 修改图纸页面
export function updatePage(data) {
  return request({
    url: '/geodrawing/page',
    method: 'put',
    data: data
  })
}

// 删除图纸页面
export function delPage(id) {
  return request({
    url: '/geodrawing/page/' + id,
    method: 'delete'
  })
}

// 识别图纸页面
export function recognizePageWithType(pageId, type) {
  
  let data = new FormData()
  data.append('drawingPageId', pageId)
  if (type) {
    data.append('type', type)
  }
  return request({
    url: `/geodrawing/page/recognizePageWithType`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 对图纸页面分类
export function classifyPage(pageId) {
  let data = new FormData()
  data.append('drawingPageId', pageId)
  return request({
    url: `/geodrawing/page/classifyPage`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 提交引线信息
export function submitLeaders(data) {
  return request({
    url: `/geodrawing/page/submitLeader`,
    method: 'post',
    data,
  })
}

// 更新视觉识别结果
export function updateVisionResult(data) {
  return request({
    url: '/geodrawing/page/updateVisionResult',
    method: 'post',
    data: data
  })
}

// 更新视觉识别结果
export function recomputeLogicResult(data) {
  return request({
    url: '/geodrawing/page/recomputeLogicResult',
    method: 'post',
    data: data
  })
}

export function updatePageText(drawingPageId, data) {
  return request({
    url: `/geodrawing/page/updatePageText/${drawingPageId}`,
    method: 'post',
    data
  })
}
import request from '@/utils/request'

// 查询审核工程列表
export function listProject(query) {
  return request({
    url: '/geodrawing/project/list',
    method: 'get',
    params: query
  })
}

// 查询审核工程详细
export function getProject(id) {
  return request({
    url: '/geodrawing/project/' + id,
    method: 'get'
  })
}

// 新增审核工程
export function addProject(data) {
  return request({
    url: '/geodrawing/project',
    method: 'post',
    data: data
  })
}

// 修改审核工程
export function updateProject(data) {
  return request({
    url: '/geodrawing/project',
    method: 'put',
    data: data
  })
}

// 删除审核工程
export function delProject(id) {
  return request({
    url: '/geodrawing/project/' + id,
    method: 'delete'
  })
}

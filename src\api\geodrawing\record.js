import request from '@/utils/request'

// 查询人工复核记录列表
export function listRecord(query) {
  return request({
    url: '/geodrawing/record/list',
    method: 'get',
    params: query
  })
}

// 查询人工复核记录详细
export function getRecord(id) {
  return request({
    url: '/geodrawing/record/' + id,
    method: 'get'
  })
}

// 新增人工复核记录
export function addRecord(data) {
  return request({
    url: '/geodrawing/record',
    method: 'post',
    data: data
  })
}

// 修改人工复核记录
export function updateRecord(data) {
  return request({
    url: '/geodrawing/record',
    method: 'put',
    data: data
  })
}

// 删除人工复核记录
export function delRecord(id) {
  return request({
    url: '/geodrawing/record/' + id,
    method: 'delete'
  })
}

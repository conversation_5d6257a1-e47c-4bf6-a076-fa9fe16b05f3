import request from '@/utils/request'

// 查询审核报告列表
export function listReport(query) {
  return request({
    url: '/geodrawing/report/list',
    method: 'get',
    params: query
  })
}
 // 查询工程审核报告列表
export function listProjectReport(query) {
  return request({
    url: '/geodrawing/report/project/list',
    method: 'get',
    params: query
  })
}

// 查询审核报告详细
export function getReport(id) {
  return request({
    url: '/geodrawing/report/' + id,
    method: 'get'
  })
}

// 新增审核报告
export function addReport(data) {
  return request({
    url: '/geodrawing/report',
    method: 'post',
    data: data
  })
}

// 修改审核报告
export function updateReport(data) {
  return request({
    url: '/geodrawing/report',
    method: 'put',
    data: data
  })
}

// 删除审核报告
export function delReport(id) {
  return request({
    url: '/geodrawing/report/' + id,
    method: 'delete'
  })
}

// 生成报告
export function genReport(drawingId, projectId) {
  const data = new FormData()
  if (drawingId) data.append('drawingId', drawingId)
  if (projectId) data.append('projectId', projectId)
  return request({
    url: '/geodrawing/report/generate',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 报告生成状态
export function repStatus(reportId) {
  const data = new FormData()
  data.append('reportId', reportId)
  return request({
    url: '/geodrawing/report/status',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

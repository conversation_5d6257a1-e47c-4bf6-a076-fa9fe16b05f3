import request from '@/utils/request'

// 查询审核结果列表
export function listResult(query) {
  return request({
    url: '/geodrawing/result/list',
    method: 'get',
    params: query
  })
}

// 查询审核结果详细
export function getResult(id) {
  return request({
    url: '/geodrawing/result/' + id,
    method: 'get'
  })
}

// 新增审核结果
export function addResult(data) {
  return request({
    url: '/geodrawing/result',
    method: 'post',
    data: data
  })
}

// 修改审核结果
export function updateResult(data) {
  return request({
    url: '/geodrawing/result',
    method: 'put',
    data: data
  })
}

// 删除审核结果
export function delResult(id) {
  return request({
    url: '/geodrawing/result/' + id,
    method: 'delete'
  })
}

import request from '@/utils/request'

// 查询识别任务列表
export function listTask(query) {
  return request({
    url: '/geodrawing/task/list',
    method: 'get',
    params: query
  })
}

// 查询识别任务详细
export function getTask(id) {
  return request({
    url: '/geodrawing/task/' + id,
    method: 'get'
  })
}

// 新增识别任务
export function addTask(data) {
  return request({
    url: '/geodrawing/task',
    method: 'post',
    data: data
  })
}

// 修改识别任务
export function updateTask(data) {
  return request({
    url: '/geodrawing/task',
    method: 'put',
    data: data
  })
}

// 删除识别任务
export function delTask(id) {
  return request({
    url: '/geodrawing/task/' + id,
    method: 'delete'
  })
}

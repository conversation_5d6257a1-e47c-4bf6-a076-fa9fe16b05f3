<template>
  <div class="anomaly-detector">
    <!-- 异常统计面板 -->
    <div v-if="showStats" class="anomaly-stats-panel">
      <div class="stats-header">
        <span class="title">🚨 异常检测</span>
        <button @click="togglePanel" class="toggle-btn">
          {{ isPanelExpanded ? '收起' : '展开' }}
        </button>
      </div>
      
      <div v-show="isPanelExpanded" class="stats-content">
        <!-- 总体统计 -->
        <div class="summary">
          <div class="stat-item total">
            <span class="label">总框数:</span>
            <span class="value">{{ totalBboxes }}</span>
          </div>
          <div class="stat-item anomaly">
            <span class="label">异常框数:</span>
            <span class="value">{{ anomalyBboxes.length }}</span>
          </div>
          <div class="stat-item rate">
            <span class="label">异常率:</span>
            <span class="value">{{ anomalyRate }}%</span>
          </div>
        </div>

        <!-- 异常类型统计 -->
        <div v-if="anomalyTypeStats && Object.keys(anomalyTypeStats).length > 0" class="type-stats">
          <div class="subsection-title">异常类型分布</div>
          <div class="type-list">
            <div 
              v-for="(count, type) in anomalyTypeStats" 
              :key="type"
              class="type-item"
              :class="{ active: selectedAnomalyTypes.includes(type) }"
              @click="toggleAnomalyType(type)"
            >
              <span class="type-name">{{ getAnomalyTypeName(type) }}</span>
              <span class="type-count">{{ count }}</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-show="anomalyBboxes.length > 0" class="actions">
          <button @click="highlightAllAnomalies" class="action-btn highlight">
            {{ isHighlightingAll ? '取消全部高亮' : '高亮所有异常' }}
          </button>
        </div>

        <!-- 异常列表 -->
        <div v-if="showAnomalyList && anomalyBboxes.length > 0" class="anomaly-list">
          <div class="subsection-title">异常框列表</div>
          <div class="list-container">
            <div 
              v-for="(anomaly, index) in filteredAnomalies"
              :key="anomaly.id || index"
              class="anomaly-item"
              :class="{ 
                active: selectedAnomalyId === anomaly.id,
                'type-filtered': selectedAnomalyTypes.length > 0 && !selectedAnomalyTypes.includes(anomaly.anomalyType)
              }"
              @click="selectAnomaly(anomaly)"
            >
              <div class="item-header">
                <span class="item-id">提示: {{ anomaly.text || `#${index}` }}</span>
                <span class="item-type" :class="`type-${anomaly.anomalyType}`">
                  {{ getAnomalyTypeName(anomaly.anomalyType) }}
                </span>
              </div>
              <div class="item-details">
                <span class="item-category">{{ categoryMap[anomaly.category] || anomaly.category }}</span>
                <span class="item-confidence" v-if="anomaly.confidence !== undefined">
                  置信度: {{ Math.round(anomaly.confidence * 100) }}%
                </span>
              </div>
              <div v-if="anomaly.anomalyDetails" class="item-description">
                {{ anomaly.anomalyDetails }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

const props = defineProps({
  // 边界框列表
  bboxList: {
    type: Array,
    default: () => []
  },
  // 异常检测结果
  anomalyResults: {
    type: Array,
    default: () => []
  },
  // 异常检测配置
  anomalyConfig: {
    type: Object,
    default: () => ({
      // 尺寸异常阈值
      minWidth: 10,
      minHeight: 10,
      maxWidth: 1000,
      maxHeight: 1000,
      // 置信度阈值
      minConfidence: 0.3,
      maxConfidence: 1.0,
      // 位置异常检测
      checkOutOfBounds: true,
      // 重叠检测
      checkOverlap: true,
      overlapThreshold: 0.8,
      // 长宽比异常
      checkAspectRatio: true,
      minAspectRatio: 0.1,
      maxAspectRatio: 10.0
    })
  },
  // 类别映射
  categoryMap: {
    type: Object,
    default: () => ({})
  },
  // 异常类型名称映射
  anomalyTypeMap: {
    type: Object,
    default: () => ({
      'spatial_conflict': '重叠异常',
      'data_inconsistency': '数据异常', 
    })
  },
  // 显示配置
  showStats: {
    type: Boolean,
    default: true
  },
  showAnomalyList: {
    type: Boolean,
    default: true
  },
  // 初始展开状态
  initialExpanded: {
    type: Boolean,
    default: true
  }
})

const emits = defineEmits([
  'anomaly-selected',
  'anomaly-highlighted',
  'type-filter-changed'
])

// 面板状态
const isPanelExpanded = ref(props.initialExpanded)
const selectedAnomalyId = ref(null)
const selectedAnomalyTypes = ref([])
const isHighlightingAll = ref(false)


// 计算异常数据
const anomalyBboxes = computed(() => {
  if (props.anomalyResults.length > 0) {
    return props.anomalyResults
  }
  return []
})

const totalBboxes = computed(() => props.bboxList.length)

const anomalyRate = computed(() => {
  if (totalBboxes.value === 0) return 0
  return Math.round((anomalyBboxes.value.length / totalBboxes.value) * 100)
})

const anomalyTypeStats = computed(() => {
  const stats = {}
  anomalyBboxes.value.forEach(anomaly => {
    const type = anomaly.anomalyType || 'unknown'
    stats[type] = (stats[type] || 0) + 1
  })
  return stats
})

const filteredAnomalies = computed(() => {
  if (selectedAnomalyTypes.value.length === 0) {
    return anomalyBboxes.value
  }
  return anomalyBboxes.value.filter(anomaly => 
    selectedAnomalyTypes.value.includes(anomaly.anomalyType)
  )
})

// 方法
const togglePanel = () => {
  isPanelExpanded.value = !isPanelExpanded.value
}

const getAnomalyTypeName = (type) => {
  return props.anomalyTypeMap[type] || type
}

const toggleAnomalyType = (type) => {
  const index = selectedAnomalyTypes.value.indexOf(type)
  if (index > -1) {
    selectedAnomalyTypes.value.splice(index, 1)
  } else {
    selectedAnomalyTypes.value.push(type)
  }
  emits('type-filter-changed', selectedAnomalyTypes.value)
}

const selectAnomaly = (anomaly) => {
  selectedAnomalyId.value = anomaly.id
  emits('anomaly-selected', anomaly)
}

const highlightAllAnomalies = () => {
  isHighlightingAll.value = !isHighlightingAll.value
  const anomalyIds = isHighlightingAll.value ? 
    anomalyBboxes.value.map(a => a.id || a.originalIndex) : []
  emits('anomaly-highlighted', anomalyIds)
}

// 监听异常类型过滤变化
watch(selectedAnomalyTypes, (newTypes) => {
  emits('type-filter-changed', newTypes)
})

// 暴露方法和数据
defineExpose({
  anomalyBboxes,
  anomalyTypeStats,
  selectAnomaly,
  highlightAllAnomalies,
  toggleAnomalyType,
})
</script>

<style scoped lang="scss">
.anomaly-detector {
  position: relative;
}

.anomaly-stats-panel {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  max-width: 320px;
  max-height: 70vh;
  overflow-y: auto;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  
  .title {
    font-weight: bold;
    font-size: 14px;
  }
  
  .toggle-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 8px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.stats-content {
  padding: 12px;
}

.summary {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  
  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .label {
      font-size: 10px;
      opacity: 0.8;
    }
    
    .value {
      font-size: 14px;
      font-weight: bold;
    }
    
    &.total .value { color: #17a2b8; }
    &.anomaly .value { color: #dc3545; }
    &.rate .value { color: #ffc107; }
  }
}

.subsection-title {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 6px;
  color: #ffc107;
}

.type-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 12px;
}

.type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  min-width: 80px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
  
  &.active {
    background: #007bff;
  }
  
  .type-name {
    flex: 1;
  }
  
  .type-count {
    background: rgba(255, 255, 255, 0.3);
    padding: 2px 6px;
    border-radius: 2px;
    font-weight: bold;
    margin-left: 6px;
  }
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.action-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  
  &.highlight {
    background: #ffc107;
    color: #212529;
    
    &:hover { background: #e0a800; }
  }
}

.anomaly-list {
  max-height: 200px;
  overflow-y: auto;
}

.list-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.anomaly-item {
  background: rgba(255, 255, 255, 0.05);
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  border-left: 3px solid transparent;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &.active {
    background: rgba(0, 123, 255, 0.2);
    border-left-color: #007bff;
  }
  
  &.type-filtered {
    opacity: 0.5;
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  
  .item-id {
    font-size: 11px;
    font-weight: bold;
  }
  
  .item-type {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 2px;
    
    &.type-size { background: #dc3545; }
    &.type-confidence { background: #ffc107; color: #212529; }
    &.type-position { background: #17a2b8; }
    &.type-overlap { background: #6f42c1; }
    &.type-aspect_ratio { background: #e83e8c; }
  }
}

.item-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  opacity: 0.8;
  margin-bottom: 2px;
}

.item-description {
  font-size: 10px;
  opacity: 0.7;
  line-height: 1.3;
}
</style>
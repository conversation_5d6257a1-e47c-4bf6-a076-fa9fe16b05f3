import { ref, onMounted, watch } from 'vue'

export function useContainerSize(props) {
  const containerRef = ref(null)
  const viewportRef = ref(null)
  const containerWidth = ref(0)
  const containerHeight = ref(0)
  const resizeObserver = ref(null)

  // 获取容器尺寸
  const updateContainerSize = () => {
    if (containerRef.value) {
      const rect = containerRef.value.getBoundingClientRect()
      containerWidth.value = rect.width
      containerHeight.value = rect.height
    }
  }

  // 计算基础显示尺寸的逻辑，需要图像状态作为参数
  const calculateBaseDisplayInfo = (imageNaturalWidth, imageNaturalHeight, imageLoaded) => {
    if (!imageLoaded || imageNaturalWidth <= 0 || imageNaturalHeight <= 0) {
      return { displayWidth: 0, displayHeight: 0, scaleX: 1, scaleY: 1 }
    }
    
    // 使用容器的实际尺寸而不是 props 中的最大尺寸
    const maxWidth = containerWidth.value || props.maxWidth
    const maxHeight = containerHeight.value || props.maxHeight
    
    const naturalRatio = imageNaturalWidth / imageNaturalHeight
    const containerRatio = maxWidth / maxHeight
    
    let width, height
    
    if (naturalRatio > containerRatio) {
      width = Math.min(maxWidth, imageNaturalWidth)
      height = width / naturalRatio
    } else {
      height = Math.min(maxHeight, imageNaturalHeight)
      width = height * naturalRatio
    }
    
    const scaleX = width / imageNaturalWidth
    const scaleY = height / imageNaturalHeight
    
    return {
      displayWidth: Math.round(width),
      displayHeight: Math.round(height),
      scaleX,
      scaleY
    }
  }

  // 清理
  const cleanup = () => {
    if (resizeObserver.value) {
      resizeObserver.value.disconnect()
      resizeObserver.value = null
    }
  }

  onMounted(() => {
    updateContainerSize()
    
    // 监听容器尺寸变化
    if (containerRef.value && window.ResizeObserver) {
      resizeObserver.value = new ResizeObserver(() => {
        updateContainerSize()
      })
      resizeObserver.value.observe(containerRef.value)
    }
  })

  // 在组件卸载时清理
  watch(() => containerRef.value, (newVal, oldVal) => {
    if (!newVal && oldVal) {
      cleanup()
    }
  })

  return {
    containerRef,
    viewportRef,
    containerWidth,
    containerHeight,
    updateContainerSize,
    calculateBaseDisplayInfo,
    cleanup
  }
}
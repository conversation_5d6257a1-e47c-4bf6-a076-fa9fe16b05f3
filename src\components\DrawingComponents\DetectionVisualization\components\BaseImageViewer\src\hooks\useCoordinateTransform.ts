import { computed } from 'vue'

export function useCoordinateTransform(props, imageState, containerSize, zoomPan) {
  // 安全数字转换
  const safeNumber = (value, defaultValue = 0) => {
    const num = Number(value)
    return isNaN(num) ? defaultValue : num
  }

  // 计算基础显示尺寸（不考虑缩放）
  const baseDisplayInfo = computed(() => {
    return containerSize.calculateBaseDisplayInfo(
      imageState.imageNaturalWidth.value,
      imageState.imageNaturalHeight.value,
      imageState.imageLoaded.value
    )
  })

  // 计算当前显示尺寸（考虑缩放）
  const currentDisplayWidth = computed(() => Math.round(baseDisplayInfo.value.displayWidth * zoomPan.zoomLevel.value))
  const currentDisplayHeight = computed(() => Math.round(baseDisplayInfo.value.displayHeight * zoomPan.zoomLevel.value))
  const currentScaleX = computed(() => baseDisplayInfo.value.scaleX * zoomPan.zoomLevel.value)
  const currentScaleY = computed(() => baseDisplayInfo.value.scaleY * zoomPan.zoomLevel.value)

  // 坐标转换工具函数
  const transformCoordinate = (value, isXAxis = true, coordsType = null) => {
    coordsType = coordsType || props.coordsType
    if (coordsType === 'relative') {
      const naturalSize = isXAxis ? imageState.imageNaturalWidth.value : imageState.imageNaturalHeight.value
      const scale = isXAxis ? currentScaleX.value : currentScaleY.value
      return safeNumber(value) * naturalSize * scale
    } else {
      const scale = isXAxis ? currentScaleX.value : currentScaleY.value
      return safeNumber(value) * scale
    }
  }

  const transformSize = (value, isWidth = true) => {
    if (props.coordsType === 'relative') {
      const naturalSize = isWidth ? imageState.imageNaturalWidth.value : imageState.imageNaturalHeight.value
      const scale = isWidth ? currentScaleX.value : currentScaleY.value
      return safeNumber(value) * naturalSize * scale
    } else {
      const scale = isWidth ? currentScaleX.value : currentScaleY.value
      return safeNumber(value) * scale
    }
  }

  const transformPoint = (point, coordsType = null) => {
    if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
      return null
    }
    
    return {
      x: transformCoordinate(point.x, true, coordsType),
      y: transformCoordinate(point.y, false, coordsType)
    }
  }

  // 逆向转换（从显示坐标转回原始坐标）
  const reverseTransformCoordinate = (value, isXAxis = true) => {
    if (props.coordsType === 'relative') {
      const naturalSize = isXAxis ? imageState.imageNaturalWidth.value : imageState.imageNaturalHeight.value
      const scale = isXAxis ? currentScaleX.value : currentScaleY.value
      return value / (naturalSize * scale)
    } else {
      const scale = isXAxis ? currentScaleX.value : currentScaleY.value
      return value / scale
    }
  }

  const reverseTransformPoint = (point) => {
    if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
      return null
    }
    
    return {
      x: reverseTransformCoordinate(point.x, true),
      y: reverseTransformCoordinate(point.y, false)
    }
  }

  return {
    baseDisplayInfo,
    currentDisplayWidth,
    currentDisplayHeight,
    currentScaleX,
    currentScaleY,
    transformCoordinate,
    transformSize,
    transformPoint,
    reverseTransformCoordinate,
    reverseTransformPoint,
    safeNumber
  }
}
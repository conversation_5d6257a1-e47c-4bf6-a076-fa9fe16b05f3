export function useEventHandlers(emits, mousePosition, zoomPan, containerSize, coordinateTransform) {
  // 鼠标事件处理器
  const handleViewerMouseMove = (event) => {
    const positionInfo = mousePosition.getMousePositionInfo(event)
    if (positionInfo) {
      emits('viewer-mousemove', {
        event,
        ...positionInfo
      })
    }
  }

  const handleViewerMouseDown = (event) => {
    const positionInfo = mousePosition.getMousePositionInfo(event)
    if (positionInfo) {
      emits('viewer-mousedown', {
        event,
        ...positionInfo
      })
    }
  }

  const handleViewerMouseUp = (event) => {
    const positionInfo = mousePosition.getMousePositionInfo(event)
    if (positionInfo) {
      emits('viewer-mouseup', {
        event,
        ...positionInfo
      })
    }
  }

  const handleViewerMouseLeave = (event) => {
    emits('viewer-mouseleave', { event })
  }

  const handleViewerWheel = (event) => {
    event.preventDefault() // 阻止默认滚动行为
    
    const positionInfo = mousePosition.getMousePositionInfo(event)
    
    if (positionInfo && positionInfo.isInImage) {
      // 计算缩放因子
      const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1
      
      // 以鼠标位置为中心进行缩放
      zoomPan.zoomAtPoint(
        positionInfo,
        zoomFactor,
        containerSize.containerWidth.value,
        containerSize.containerHeight.value,
        coordinateTransform.currentDisplayWidth,
        coordinateTransform.currentDisplayHeight,
        mousePosition.centerOffset
      )
    }
    
    // 发出事件
    emits('viewer-wheel', {
      event,
      ...positionInfo
    })
  }

  return {
    handleViewerMouseMove,
    handleViewerMouseDown,
    handleViewerMouseUp,
    handleViewerMouseLeave,
    handleViewerWheel
  }
}
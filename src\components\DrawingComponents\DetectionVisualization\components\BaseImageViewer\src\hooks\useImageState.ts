import { ref, computed, watch, nextTick } from 'vue'

export function useImageState(props, emits) {
  // 响应式数据
  const imageRef = ref(null)
  const imageLoaded = ref(false)
  const imageLoading = ref(false)
  const imageError = ref(false)
  const imageNaturalWidth = ref(0)
  const imageNaturalHeight = ref(0)

  // 计算基础显示尺寸（不考虑缩放）
  const baseDisplayInfo = computed(() => {
    if (!imageLoaded.value || imageNaturalWidth.value <= 0 || imageNaturalHeight.value <= 0) {
      return { displayWidth: 0, displayHeight: 0, scaleX: 1, scaleY: 1 }
    }
    
    // 这里需要从容器尺寸获取，会在组件中传入实际容器尺寸
    return { displayWidth: 0, displayHeight: 0, scaleX: 1, scaleY: 1 }
  })

  // 为了保持向后兼容性，保留原始的计算属性
  const displayWidth = computed(() => baseDisplayInfo.value.displayWidth)
  const displayHeight = computed(() => baseDisplayInfo.value.displayHeight)
  const scaleX = computed(() => baseDisplayInfo.value.scaleX)
  const scaleY = computed(() => baseDisplayInfo.value.scaleY)

  // 事件处理
  const handleImageLoadStart = () => {
    imageLoading.value = true
    imageError.value = false
  }

  const handleImageLoad = () => {
    const img = imageRef.value
    if (!img) return
    
    try {
      imageNaturalWidth.value = img.naturalWidth || 0
      imageNaturalHeight.value = img.naturalHeight || 0
      
      if (imageNaturalWidth.value > 0 && imageNaturalHeight.value > 0) {
        imageLoaded.value = true
        
        emits('image-loaded', {
          width: imageNaturalWidth.value,
          height: imageNaturalHeight.value,
          displayWidth: displayWidth.value,
          displayHeight: displayHeight.value
        })
      } else {
        throw new Error('图像尺寸无效')
      }
    } catch (error) {
      console.error('图像加载处理失败:', error)
      handleImageError(error)
    } finally {
      imageLoading.value = false
    }
  }

  const handleImageError = (error) => {
    imageLoading.value = false
    imageLoaded.value = false
    imageError.value = true
    emits('image-error', error)
  }

  const retryLoad = () => {
    if (!props.imageSrc) return
    
    imageError.value = false
    imageLoaded.value = false
    
    nextTick(() => {
      if (imageRef.value) {
        imageRef.value.src = props.imageSrc + '?t=' + Date.now()
      }
    })
  }

  const resetImageState = () => {
    imageLoaded.value = false
    imageError.value = false
    imageLoading.value = false
    imageNaturalWidth.value = 0
    imageNaturalHeight.value = 0
  }

  // 监听 imageSrc 变化
  watch(() => props.imageSrc, (newSrc, oldSrc) => {
    if (newSrc !== oldSrc) {
      resetImageState()
    }
  }, { immediate: true })

  return {
    // 响应式数据
    imageRef,
    imageLoaded,
    imageLoading,
    imageError,
    imageNaturalWidth,
    imageNaturalHeight,
    
    // 计算属性
    baseDisplayInfo,
    displayWidth,
    displayHeight,
    scaleX,
    scaleY,
    
    // 方法
    handleImageLoadStart,
    handleImageLoad,
    handleImageError,
    retryLoad,
    resetImageState
  }
}
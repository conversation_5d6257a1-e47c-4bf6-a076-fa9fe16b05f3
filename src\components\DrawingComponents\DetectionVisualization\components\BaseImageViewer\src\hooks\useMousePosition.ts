import { computed } from 'vue'

export function useMousePosition(containerSize, coordinateTransform, zoomPan) {
  // 计算居中偏移
  const centerOffset = computed(() => {
    const containerW = containerSize.containerWidth.value || 800
    const containerH = containerSize.containerHeight.value || 600
    
    return {
      x: Math.max(0, (containerW - coordinateTransform.currentDisplayWidth.value) / 2),
      y: Math.max(0, (containerH - coordinateTransform.currentDisplayHeight.value) / 2)
    }
  })

  // 计算图像包装器的偏移
  const wrapperOffset = () => {
    return {
      x: centerOffset.value.x + zoomPan.panOffset.value.x,
      y: centerOffset.value.y + zoomPan.panOffset.value.y
    }
  }

  // 获取鼠标位置信息的工具函数
  const getMousePositionInfo = (event) => {
    if (!containerSize.containerRef.value) {
      return null
    }

    const containerRect = containerSize.containerRef.value.getBoundingClientRect()
    const wrapper = wrapperOffset()
    
    // 相对于容器的坐标
    const containerCoords = {
      x: event.clientX - containerRect.left,
      y: event.clientY - containerRect.top
    }
    
    // 相对于图像包装器的坐标
    const wrapperCoords = {
      x: containerCoords.x - wrapper.x,
      y: containerCoords.y - wrapper.y
    }
    
    // 相对于图像的坐标（显示坐标系）
    const imageCoords = {
      x: wrapperCoords.x,
      y: wrapperCoords.y
    }
    
    // 转换为原始图像坐标系
    const originalCoords = coordinateTransform.reverseTransformPoint(imageCoords)
    
    // 归一化坐标（0-1）
    const normalizedCoords = {
      x: imageCoords.x / coordinateTransform.currentDisplayWidth.value,
      y: imageCoords.y / coordinateTransform.currentDisplayHeight.value
    }
    
    // 检查是否在图像区域内
    const isInImage = wrapperCoords.x >= 0 && 
                     wrapperCoords.x <= coordinateTransform.currentDisplayWidth.value &&
                     wrapperCoords.y >= 0 && 
                     wrapperCoords.y <= coordinateTransform.currentDisplayHeight.value

    return {
      containerCoords,
      wrapperCoords,
      imageCoords,
      originalCoords,
      normalizedCoords,
      isInImage,
      containerRect,
      wrapperOffset: wrapper
    }
  }

  return {
    centerOffset,
    wrapperOffset,
    getMousePositionInfo
  }
}
import { ref, computed, watch, nextTick } from 'vue'

export function useZoomPan(props, imageState) {
  // 缩放和平移状态
  const zoomLevel = ref(1)
  const panOffset = ref({ x: 0, y: 0 })

  // 缩放方法
  const setZoom = (zoom) => {
    if (!props.enableZoom) return
    
    const newZoom = Math.max(props.minZoom, Math.min(props.maxZoom, zoom))
    zoomLevel.value = newZoom
    
    // 限制平移范围
    constrainPanOffset()
  }

  const adjustZoom = (scaleFactor) => {
    if (!props.enableZoom) return
    
    const newZoom = zoomLevel.value * scaleFactor
    setZoom(newZoom)
  }

  // 以指定点为中心进行缩放
  const zoomAtPoint = (point, scaleFactor, containerWidth, containerHeight, currentDisplayWidth, currentDisplayHeight, centerOffset) => {
    if (!props.enableZoom || !point) return
    
    const oldZoom = zoomLevel.value
    const newZoom = Math.max(props.minZoom, Math.min(props.maxZoom, oldZoom * scaleFactor))
    
    if (newZoom === oldZoom) return // 缩放级别没有变化
    
    // 计算鼠标在容器中的位置
    const mouseContainerX = point.containerCoords.x
    const mouseContainerY = point.containerCoords.y
    
    // 计算鼠标在图像中的位置（相对于图像左上角）
    const mouseImageX = point.imageCoords.x
    const mouseImageY = point.imageCoords.y
    
    // 计算缩放比例变化
    const zoomRatio = newZoom / oldZoom
    
    // 设置新的缩放级别
    zoomLevel.value = newZoom
    
    // 等待下一帧确保缩放已应用
    nextTick(() => {
      // 计算缩放后鼠标指向的图像位置的新坐标
      const newMouseImageX = mouseImageX * zoomRatio
      const newMouseImageY = mouseImageY * zoomRatio
      
      // 计算新的图像包装器位置，使鼠标仍指向同一图像内容
      const newWrapperX = mouseContainerX - newMouseImageX
      const newWrapperY = mouseContainerY - newMouseImageY
      
      // 计算新的居中偏移
      const newCenterOffsetX = Math.max(0, (containerWidth - currentDisplayWidth.value) / 2)
      const newCenterOffsetY = Math.max(0, (containerHeight - currentDisplayHeight.value) / 2)
      
      // 计算所需的平移偏移
      const requiredPanX = newWrapperX - newCenterOffsetX
      const requiredPanY = newWrapperY - newCenterOffsetY
      
      // 更新平移偏移
      panOffset.value = {
        x: requiredPanX,
        y: requiredPanY
      }
      
      // 约束平移范围
      constrainPanOffset()
    })
  }

  // 平移方法
  const setPanOffset = (offset) => {
    if (!props.enablePan) return
    
    panOffset.value = { ...offset }
    constrainPanOffset()
  }

  const adjustPanOffset = (deltaX, deltaY) => {
    if (!props.enablePan) return
    
    panOffset.value = {
      x: panOffset.value.x + deltaX,
      y: panOffset.value.y + deltaY
    }
    
    constrainPanOffset()
  }

  // 约束平移范围，防止图像移出视口太远
  const constrainPanOffset = () => {
    // 可以在这里添加约束逻辑
    // 目前保持简单实现
  }

  // 重置视图
  const resetView = () => {
    zoomLevel.value = 1
    panOffset.value = { x: 0, y: 0 }
  }

  // 缩放到适合大小
  const zoomToFit = () => {
    zoomLevel.value = 1
    panOffset.value = { x: 0, y: 0 }
  }

  // 缩放到实际大小
  const zoomToActual = (baseDisplayInfo, imageNaturalWidth, imageNaturalHeight) => {
    if (!baseDisplayInfo || !imageNaturalWidth || !imageNaturalHeight) return
    
    // 计算实际大小相对于基础显示大小的缩放比例
    const actualZoom = Math.min(
      imageNaturalWidth / baseDisplayInfo.displayWidth,
      imageNaturalHeight / baseDisplayInfo.displayHeight
    )
    
    setZoom(actualZoom)
  }

  const absoluteToRelative = (pos) => {
    if (!pos || typeof pos.x !== 'number' || typeof pos.y !== 'number') {
      return null
    }
    return {
      x: pos.x / imageState.imageNaturalWidth.value,
      y: pos.y / imageState.imageNaturalHeight.value
    }
  }

  // 缩放到指定坐标点
  const zoomToPoint = (point, zoom, coordsType, transformPoint, containerWidth, containerHeight, currentDisplayWidth, currentDisplayHeight, centerOffset) => {
    if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
      console.warn('无效的坐标点或图像未加载')
      return
    }

    // 如果是绝对，先转换坐标为相对
    if (coordsType === 'absolute') {
      point = absoluteToRelative(point)
      coordsType = 'relative'
    }

    // 先设置新的缩放级别
    const targetZoom = Math.max(props.minZoom, Math.min(props.maxZoom, zoom))
    zoomLevel.value = targetZoom
    
    // 等待下一帧确保缩放已应用
    nextTick(() => {
      const { x: displayX, y: displayY } = transformPoint(point, coordsType)
      
      // 计算容器中心点
      const containerCenterX = containerWidth / 2
      const containerCenterY = containerHeight / 2
      
      // 计算当前图像的基础居中位置（不考虑平移）
      const baseCenterX = centerOffset.value.x + currentDisplayWidth.value / 2
      const baseCenterY = centerOffset.value.y + currentDisplayHeight.value / 2

      // 计算目标点相对于图像中心的偏移
      const offsetFromImageCenterX = displayX - currentDisplayWidth.value / 2
      const offsetFromImageCenterY = displayY - currentDisplayHeight.value / 2
      
      // 计算需要的平移，使目标点移动到容器中心
      const requiredPanX = containerCenterX - baseCenterX - offsetFromImageCenterX
      const requiredPanY = containerCenterY - baseCenterY - offsetFromImageCenterY
      
      // 设置平移偏移
      panOffset.value = {
        x: requiredPanX,
        y: requiredPanY
      }
    })
  }

  // 监听缩放变化，调整平移范围
  watch(zoomLevel, () => {
    constrainPanOffset()
  })

  return {
    zoomLevel,
    panOffset,
    setZoom,
    adjustZoom,
    zoomAtPoint,
    setPanOffset,
    adjustPanOffset,
    resetView,
    zoomToFit,
    zoomToActual,
    zoomToPoint,
    constrainPanOffset
  }
}
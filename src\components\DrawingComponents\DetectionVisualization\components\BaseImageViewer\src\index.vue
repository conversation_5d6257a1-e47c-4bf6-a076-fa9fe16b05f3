<template>
  <div
    class="base-image-viewer"
    ref="containerRef"
    @mousemove="handleViewerMouseMove"
    @mousedown="handleViewerMouseDown"
    @mouseup="handleViewerMouseUp"
    @mouseleave="handleViewerMouseLeave"
    @wheel="handleViewerWheel"
  >
    <!-- 空状态提示 -->
    <div v-if="!imageSrc || !imageSrc.trim()" class="empty-state">
      <div class="empty-icon">📷</div>
      <p class="empty-text">{{ emptyText }}</p>
    </div>
    
    <!-- 加载状态 -->
    <div v-else-if="imageLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">图像加载中...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="imageError" class="error-state">
      <div class="error-icon">❌</div>
      <p class="error-text">图像加载失败</p>
      <button @click="retryLoad" class="retry-button">重试</button>
    </div>
    
    <!-- 正常显示状态 -->
    <div v-else class="image-wrapper">
      <!-- 视口容器 -->
      <div 
        class="viewport-container" 
        :style="viewportStyle"
        ref="viewportRef"
      >
        <!-- 主图像 -->
        <img
          ref="imageRef"
          :src="imageSrc"
          @load="handleImageLoad"
          @error="handleImageError"
          @loadstart="handleImageLoadStart"
          class="main-image"
          :style="imageStyle"
          :alt="imageAlt"
        />
        
        <!-- SVG覆盖层 -->
        <svg
          v-if="imageLoaded"
          class="overlay-svg"
          :width="currentDisplayWidth"
          :height="currentDisplayHeight"
          :style="overlayStyle"
          v-bind="$attrs"
        >
          <slot
            :displayWidth="currentDisplayWidth"
            :displayHeight="currentDisplayHeight"
            :scaleX="currentScaleX"
            :scaleY="currentScaleY"
            :transformCoordinate="transformCoordinate"
            :transformSize="transformSize"
            :transformPoint="transformPoint"
            :reverseTransformPoint="reverseTransformPoint"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import { 
  useContainerSize,
  useCoordinateTransform,
  useEventHandlers,
  useImageState,
  useMousePosition,
  useZoomPan,
} from './hooks'


const props = defineProps({
  // 图像源
  imageSrc: {
    type: String,
    default: null
  },
  // 控件最大宽度
  maxWidth: {
    type: Number,
    default: 800
  },
  // 控件最大高度
  maxHeight: {
    type: Number,
    default: 600
  },
  // 空状态最小尺寸
  minWidth: {
    type: Number,
    default: 300
  },
  minHeight: {
    type: Number,
    default: 200
  },
  // 坐标类型: 'absolute' | 'relative'
  coordsType: {
    type: String,
    default: 'absolute',
    validator: (value) => ['absolute', 'relative'].includes(value)
  },
  // 自定义文本
  emptyText: {
    type: String,
    default: '暂无图像'
  },
  imageAlt: {
    type: String,
    default: '图像'
  },
  // 缩放配置
  enableZoom: {
    type: Boolean,
    default: true
  },
  enablePan: {
    type: Boolean,
    default: true
  },
  minZoom: {
    type: Number,
    default: 0.1
  },
  maxZoom: {
    type: Number,
    default: 5
  }
})

const emits = defineEmits([
  'image-loaded', 
  'image-error',
  'viewer-mousemove',
  'viewer-mousedown', 
  'viewer-mouseup',
  'viewer-mouseleave',
  'viewer-wheel'
])

// 图像状态管理
const imageState = useImageState(props, emits), {
  imageRef,
  imageLoaded,
  imageLoading,
  imageError,
  imageNaturalWidth,
  imageNaturalHeight,
  handleImageLoadStart,
  handleImageLoad,
  handleImageError,
  retryLoad,
  resetImageState
} = imageState

// 容器尺寸管理
const containerSize = useContainerSize(props), {
  containerRef,
  viewportRef,
  containerWidth,
  containerHeight,
  updateContainerSize
} = containerSize

// 缩放和平移管理
const zoomPan = useZoomPan(props, imageState), {
  zoomLevel,
  panOffset,
  setZoom,
  adjustZoom,
  zoomAtPoint,
  setPanOffset,
  adjustPanOffset,
  resetView,
  zoomToFit,
  zoomToActual
} = zoomPan

// 坐标转换工具
const coordinateTransform = useCoordinateTransform(props, imageState, containerSize, zoomPan), {
  baseDisplayInfo,
  currentDisplayWidth,
  currentDisplayHeight,
  currentScaleX,
  currentScaleY,
  transformCoordinate,
  transformSize,
  transformPoint,
  reverseTransformPoint
} = coordinateTransform

// 鼠标位置工具
const mousePosition = useMousePosition(containerSize, coordinateTransform, zoomPan), { 
  centerOffset
} = mousePosition

// 事件管理
const eventHandlers = useEventHandlers(emits, mousePosition, zoomPan, containerSize, coordinateTransform), {
  handleViewerMouseMove,
  handleViewerMouseDown,
  handleViewerMouseUp,
  handleViewerMouseLeave,
  handleViewerWheel
} = eventHandlers


const displayWidth = computed(() => baseDisplayInfo.value.displayWidth)
const displayHeight = computed(() => baseDisplayInfo.value.displayHeight)
const scaleX = computed(() => baseDisplayInfo.value.scaleX)
const scaleY = computed(() => baseDisplayInfo.value.scaleY)

// 样式计算
const viewportStyle = computed(() => ({
  width: currentDisplayWidth.value + 'px',
  height: currentDisplayHeight.value + 'px',
  transform: `translate(${centerOffset.value.x + panOffset.value.x}px, ${centerOffset.value.y + panOffset.value.y}px)`,
  transformOrigin: '0 0',
  position: 'absolute',
  top: '0',
  left: '0',
  transition: 'none'
}))

const imageStyle = computed(() => ({
  width: currentDisplayWidth.value + 'px',
  height: currentDisplayHeight.value + 'px',
  display: 'block'
}))

const overlayStyle = computed(() => ({
  position: 'absolute',
  top: '0',
  left: '0',
  pointerEvents: 'auto'
}))

// 增强的缩放到指定点方法
const zoomToPoint = (point, zoom, coordsType = null) => {
  return zoomPan.zoomToPoint(
    point, 
    zoom, 
    coordsType || props.coordsType, 
    transformPoint, 
    containerWidth.value || props.maxWidth, 
    containerHeight.value || props.maxHeight, 
    currentDisplayWidth, 
    currentDisplayHeight, 
    centerOffset
  )
}

// 增强的缩放到实际大小方法
const enhancedZoomToActual = () => {
  zoomPan.zoomToActual(baseDisplayInfo.value, imageNaturalWidth.value, imageNaturalHeight.value)
}

// 监听图像源变化时重置所有状态
watch(() => props.imageSrc, () => {
  resetView()
})

// 暴露方法和数据
defineExpose({
  // 状态
  imageLoaded,
  displayWidth,
  displayHeight,
  scaleX,
  scaleY,
  imageNaturalWidth,
  imageNaturalHeight,
  zoomLevel,
  panOffset,
  
  // 当前状态（考虑缩放）
  currentDisplayWidth,
  currentDisplayHeight,
  currentScaleX,
  currentScaleY,
  
  // 转换方法
  transformCoordinate,
  transformSize,
  transformPoint,
  reverseTransformPoint,
  
  // 控制方法
  setZoom,
  adjustZoom,
  zoomAtPoint,
  setPanOffset,
  adjustPanOffset,
  resetView,
  zoomToFit,
  zoomToActual: enhancedZoomToActual,
  zoomToPoint,
  retryLoad,
  
  // 容器尺寸
  containerWidth,
  containerHeight,
  updateContainerSize,
  
  // 鼠标位置工具
  getMousePositionInfo: mousePosition.getMousePositionInfo
})
</script>

<style scoped>
.base-image-viewer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f9f9f9;
  box-sizing: border-box;
}

.image-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.viewport-container {
  position: absolute;
  transform-origin: 0 0;
}

.main-image {
  display: block;
  max-width: none;
  max-height: none;
  object-fit: contain;
}

.overlay-svg {
  user-select: none;
}

/* 状态样式 */
.empty-state,
.loading-state,
.error-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6c757d;
  box-sizing: border-box;
}

.empty-icon,
.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text,
.loading-text,
.error-text {
  font-size: 16px;
  margin: 0;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  margin-top: 12px;
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-button:hover {
  background-color: #0056b3;
}
</style>
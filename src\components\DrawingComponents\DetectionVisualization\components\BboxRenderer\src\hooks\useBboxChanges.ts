import { ref, computed } from 'vue'

export function useBboxChanges(emits) {
  // 未保存的更改
  const pendingChanges = ref(new Map())
  const hasUnsavedChanges = computed(() => pendingChanges.value.size > 0)

  // 添加临时更改
  const addTempChange = (index, bbox, changeType) => {
    const existingChange = pendingChanges.value.get(index)
    
    // 如果已存在变更记录
    if (existingChange) {
      // 如果原来是 add 状态
      if (existingChange.changeType === 'add') {
        if (changeType === 'delete') {
          // add 后 delete，直接移除变更记录
          pendingChanges.value.delete(index)
          return
        } else {
          // add 后 drag/resize，保持 add 状态，只更新 bbox
          pendingChanges.value.set(index, {
            ...existingChange,
            bbox,
            timestamp: Date.now()
          })
          return
        }
      }
      
      // 如果原来是其他状态（drag/resize/delete），直接更新
      pendingChanges.value.set(index, {
        index,
        bbox,
        changeType,
        timestamp: Date.now()
      })
    } else {
      // 没有现有变更，直接添加新的
      pendingChanges.value.set(index, {
        index,
        bbox,
        changeType,
        timestamp: Date.now()
      })
    }
  }

  // 移除临时更改
  const removeTempChange = (index) => {
    pendingChanges.value.delete(index)
  }

  // 清空所有临时更改
  const clearAllChanges = () => {
    pendingChanges.value.clear()
  }

  // 获取所有待保存的更改
  const getAllChanges = () => {
    return Array.from(pendingChanges.value.values())
  }

  // 获取特定索引的更改
  const getChange = (index) => {
    return pendingChanges.value.get(index)
  }

  // 是否有特定索引的更改
  const hasChange = (index) => {
    return pendingChanges.value.has(index)
  }

  // 获取更改统计
  const changeStats = computed(() => {
    const changes = Array.from(pendingChanges.value.values())
    return {
      total: changes.length,
      drag: changes.filter(c => c.changeType === 'drag').length,
      resize: changes.filter(c => c.changeType === 'resize').length,
      add: changes.filter(c => c.changeType === 'add').length,
      delete: changes.filter(c => c.changeType === 'delete').length,
      hasChanges: changes.length > 0
    }
  })

  // 监听临时更新事件
  const handleTempUpdate = (data) => {
    addTempChange(data.index, data.bbox, data.type)
    emits('bbox-temp-updated', data)
    emits('changes-updated', {
      hasChanges: hasUnsavedChanges.value,
      stats: changeStats.value,
      changes: getAllChanges()
    })
  }

  // 保存所有更改
  const saveAllChanges = () => {
    const changes = getAllChanges()
    emits('bbox-updated', { changes })
    clearAllChanges()
    emits('changes-updated', {
      hasChanges: false,
      stats: changeStats.value,
      changes: []
    })
  }

  // 取消所有更改
  const cancelAllChanges = () => {
    clearAllChanges()
    emits('bbox-temp-cancel')
    emits('changes-updated', {
      hasChanges: false,
      stats: changeStats.value,
      changes: []
    })
  }

  return {
    // 状态
    pendingChanges,
    hasUnsavedChanges,
    changeStats,
    
    // 方法
    addTempChange,
    removeTempChange,
    clearAllChanges,
    getAllChanges,
    getChange,
    hasChange,
    handleTempUpdate,
    saveAllChanges,
    cancelAllChanges
  }
}
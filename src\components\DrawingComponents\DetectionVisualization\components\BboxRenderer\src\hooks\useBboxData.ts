import { computed, ref } from 'vue'

export function useBboxData(props) {
  // 预定义颜色
  const defaultColors = [
    '#FF6B6B', '#82E0AA', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]

  // 样式常量
  const labelHeight = 14
  const labelPadding = 4

  // 生成随机颜色
  const generateRandomColor = () => {
    return `#${Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')}`
  }

  // 获取所有类别
  const allCategories = computed(() => {
    const categories = new Set(props.allCategories)
    props.bboxList.forEach(bbox => {
      if (bbox.label) {
        categories.add(bbox.label)
      }
    })
    return Array.from(categories)
  })

  const displayedLabel = (type) => {
    return props.categoryMap && props.categoryMap[type] ? props.categoryMap[type] : type
  }

  // 生成 label 到颜色的映射
  const labelColorMap = computed(() => {
    const map = {}
    const usedLabels = []
    
    // 收集所有 label
    props.bboxList.forEach((bbox, index) => {
      let label = bbox.label
      if (!label) {
        label = `Object ${index + 1}`
      }
      if (!usedLabels.includes(label)) {
        usedLabels.push(label)
      }
    })
    
    // 确保新bbox类别也有颜色
    if (props.newBboxCategory && !usedLabels.includes(props.newBboxCategory)) {
      usedLabels.push(props.newBboxCategory)
    }
    
    usedLabels.forEach((label, idx) => {
      map[label] = idx < defaultColors.length ? defaultColors[idx] : generateRandomColor()
    })
    
    return map
  })

  // 获取新bbox的颜色
  const newBboxColor = computed(() => {
    return labelColorMap.value[props.newBboxCategory] || '#FF6B6B'
  })

  // 计算bbox的四个角点
  const getBboxCorners = (bbox) => {
    return [
      { x: bbox.x, y: bbox.y }, // 左上
      { x: bbox.x + bbox.width, y: bbox.y }, // 右上
      { x: bbox.x + bbox.width, y: bbox.y + bbox.height }, // 右下
      { x: bbox.x, y: bbox.y + bbox.height } // 左下
    ]
  }

  // 处理单个bbox
  const processBbox = (bbox, index) => {
    const label = bbox.label || `Box ${index + 1}`
    const displayed = displayedLabel(label)
    const color = bbox.color || labelColorMap.value[label]

    const isHighlighted = props.highlightKeys && props.highlightKeys.includes(bbox[props.keyField])
    const isAnimation = isHighlighted && props.highlightAnimationKeys && props.highlightAnimationKeys.includes(bbox[props.keyField])
    
    const transformedBbox = {
      x: props.transformCoordinate(bbox.x, true),
      y: props.transformCoordinate(bbox.y, false),
      width: props.transformSize(bbox.width, true),
      height: props.transformSize(bbox.height, false),
      label: String(label),
      color,
      strokeWidth: props.strokeWidth,
      labelWidth: Math.max(String(displayed).length * 8 + labelPadding * 2, 40),
      originalIndex: index,
      originalBbox: bbox,
      isHighlighted: isHighlighted,
      isAnimation: isAnimation,
      isSelected: props.selectedBboxIndex === index,
      corners: undefined
    }
    
    // 计算角点
    transformedBbox.corners = getBboxCorners(transformedBbox)
    
    return transformedBbox
  }

  // 处理边界框列表
  const processBboxes = (bboxList) => {
    return bboxList
      .filter(bbox => 
        bbox && 
        typeof bbox.x === 'number' && 
        typeof bbox.y === 'number' && 
        typeof bbox.width === 'number' && 
        typeof bbox.height === 'number' &&
        bbox.width > 0 && 
        bbox.height > 0
      )
      .map((bbox, index) => processBbox(bbox, index))
  }

  const preProcessedBboxes = computed(() => {
    if (!Array.isArray(props.bboxList) || props.bboxList.length === 0) {
      return []
    }
    return processBboxes(props.bboxList)
  })

  // 处理后的边界框
  const processedBboxes = computed(() => {
    return preProcessedBboxes.value.filter(bbox => !bbox.originalBbox[props.deleteField])
  })

  // 过滤后的边界框（根据类别过滤）
  const filteredProcessedBboxes = computed(() => {
    if (props.showAllCategories || props.visibleCategories.length === 0) {
      return processedBboxes.value
    }
    
    return processedBboxes.value.filter(bbox => {
      return props.visibleCategories.includes(bbox.label)
    })
  })

  return {
    // 常量
    defaultColors,
    labelHeight,
    labelPadding,
    
    // 计算属性
    allCategories,
    labelColorMap,
    newBboxColor,
    processedBboxes,
    preProcessedBboxes,
    filteredProcessedBboxes,
    
    // 方法
    displayedLabel,
    generateRandomColor,
    getBboxCorners,
    processBbox,
    processBboxes
  }
}
import { ref } from 'vue'

export function useBboxDrag(props, emits, bboxChanges) {

  const dragThreshold = 1 // 拖动像素阈值

  const isDragging = ref(false)
  const dragData = ref(null)
  const previewDragBbox = ref(null)

  // 状态跟踪
  const hasActuallyDragged = ref(false)

  const { handleTempUpdate } = bboxChanges

  // 更新拖拽
  const updateDrag = (mouseEvent) => {
    if (!isDragging.value || !dragData.value) return
    
    const deltaX = mouseEvent.clientX - dragData.value.startMouse.x
    const deltaY = mouseEvent.clientY - dragData.value.startMouse.y
    
    // 检测是否真正发生了拖拽（移动距离超过阈值）
    if (Math.abs(deltaX) > dragThreshold || Math.abs(deltaY) > dragThreshold) {
      hasActuallyDragged.value = true
    }

    const newX = dragData.value.startBbox.x + deltaX
    const newY = dragData.value.startBbox.y + deltaY
    
    // 立即更新预览状态
    previewDragBbox.value = {
      x: newX,
      y: newY,
      width: dragData.value.startBbox.width,
      height: dragData.value.startBbox.height
    }
  }

  // 完成拖拽
  const finishDrag = () => {
    if (!isDragging.value || !dragData.value || !previewDragBbox.value) {
      isDragging.value = false
      dragData.value = null
      previewDragBbox.value = null
      return
    }
    
    // 转换回原始坐标并发送临时更新事件
    if (hasActuallyDragged.value && previewDragBbox.value && props.reverseTransformPoint) {
      const originalStart = props.reverseTransformPoint({ 
        x: previewDragBbox.value.x, 
        y: previewDragBbox.value.y 
      })
      
      if (originalStart) {
        const updatedBbox = {
          ...dragData.value.bbox.originalBbox,
          x: originalStart.x,
          y: originalStart.y
        }
        
        // 发送临时更新事件
        handleTempUpdate({
          index: dragData.value.bbox.originalBbox.id,
          bbox: updatedBbox,
          type: 'drag'
        })
      }
    }
    
    // 清理状态
    isDragging.value = false
    dragData.value = null
    previewDragBbox.value = null

    // 延迟重置拖拽标志，防止立即触发的点击事件
    setTimeout(() => {
      hasActuallyDragged.value = false
    }, 50)
  }

  // 取消拖拽
  const cancelDrag = () => {
    isDragging.value = false
    dragData.value = null
    previewDragBbox.value = null
    hasActuallyDragged.value = false
  }

  // 获取当前拖拽中的bbox数据
  const getCurrentDragBbox = (originalBbox) => {
    if (!isDragging.value || !dragData.value || !previewDragBbox.value) {
      return null
    }
    
    if (dragData.value.bbox.originalIndex === originalBbox.originalIndex) {
      return previewDragBbox.value
    }
    
    return null
  }

  return {
    // 状态
    isDragging,
    dragData,
    previewDragBbox,
    hasActuallyDragged,
    
    // 拖拽方法
    updateDrag,
    finishDrag,
    cancelDrag,
    getCurrentDragBbox,
  }
}
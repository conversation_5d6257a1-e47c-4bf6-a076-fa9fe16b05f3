import { ref, computed } from 'vue'
import { v4 as uuidv4 } from 'uuid';

export function useBboxDrawing(props, emits, bboxData, bboxChanges) {

  const { handleTempUpdate } = bboxChanges

  // 绘制状态
  const isDrawing = ref(false)
  const drawingBbox = ref(null)
  const startPoint = ref(null)

  // 绘制控制标志
  const shouldStartDrawing = ref(false)
  const lastClickTarget = ref(null)

  // 最小框体尺寸
  const minBboxSize = 2

  const canDrawing = computed(() => {
    return props.isEditMode && !props.showAllCategories && props.visibleCategories.length === 1 && props.selectedBboxIndex === null
  })

  // 开始绘制bbox
  const startDrawing = (point, clickTarget = null) => {
    if (!canDrawing.value) return

    // 如果点击的是bbox相关元素，不开始绘制
    if (clickTarget && isBboxElement(clickTarget)) {
      shouldStartDrawing.value = false
      return
    }
    
    shouldStartDrawing.value = true
    lastClickTarget.value = clickTarget
    
    isDrawing.value = true
    startPoint.value = point
    drawingBbox.value = {
      x: point.x,
      y: point.y,
      width: 0,
      height: 0
    }
  }

  // 检查是否是bbox相关元素
  const isBboxElement = (element) => {
    if (!element) return false
    
    // 检查是否是bbox相关的类名或元素
    const bboxClasses = [
      'bbox-rect',
      'bbox-label-bg', 
      'bbox-label',
      'resize-handle',
      'delete-button',
      'delete-bg',
      'delete-icon',
      'corner-point',
      'change-indicator'
    ]
    
    // 向上遍历DOM树检查
    let current = element
    while (current && current !== document.body) {
      if (current.classList) {
        for (const className of bboxClasses) {
          if (current.classList.contains(className)) {
            return true
          }
        }
      }
      
      // 检查父级元素的类名
      if (current.closest && current.closest('.resize-handles, .edit-controls-layer')) {
        return true
      }
      
      current = current.parentElement
    }
    
    return false
  }

  // 更新绘制中的bbox
  const updateDrawing = (point) => {
    if (!isDrawing.value || !startPoint.value) return
    
    const minX = Math.min(startPoint.value.x, point.x)
    const minY = Math.min(startPoint.value.y, point.y)
    const maxX = Math.max(startPoint.value.x, point.x)
    const maxY = Math.max(startPoint.value.y, point.y)
    
    drawingBbox.value = {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    }
  }

  // 完成绘制
  const finishDrawing = () => {
    if (!isDrawing.value || !drawingBbox.value || !props.reverseTransformPoint) {
      cancelDrawing()
      return
    }
    
    // 检查bbox大小是否足够大
    if (drawingBbox.value.width < minBboxSize || drawingBbox.value.height < minBboxSize) {
      cancelDrawing()
      return
    }
    
    // 转换回原始坐标
    const startOriginal = props.reverseTransformPoint({
      x: drawingBbox.value.x,
      y: drawingBbox.value.y
    })
    
    const endOriginal = props.reverseTransformPoint({
      x: drawingBbox.value.x + drawingBbox.value.width,
      y: drawingBbox.value.y + drawingBbox.value.height
    })
    
    if (startOriginal && endOriginal) {
      const newBbox = {
        x: Math.min(startOriginal.x, endOriginal.x),
        y: Math.min(startOriginal.y, endOriginal.y),
        width: Math.abs(endOriginal.x - startOriginal.x),
        height: Math.abs(endOriginal.y - startOriginal.y),
        label: props.newBboxCategory,
        id: uuidv4(),
      }
      handleTempUpdate({
        index: newBbox.id,
        bbox: newBbox,
        type: 'add'
      })
    }
    
    cancelDrawing()
  }

  // 取消绘制
  const cancelDrawing = () => {
    isDrawing.value = false
    drawingBbox.value = null
    startPoint.value = null
    shouldStartDrawing.value = false
    lastClickTarget.value = null
  }

  // 检查是否正在绘制
  const checkDrawingStatus = () => {
    return {
      isDrawing: isDrawing.value,
      drawingBbox: drawingBbox.value,
      startPoint: startPoint.value,
      shouldStartDrawing: shouldStartDrawing.value
    }
  }

  // 绘制提示信息
  const drawingHint = computed(() => {
    if (!isDrawing.value || !drawingBbox.value) return null
    
    const width = Math.round(drawingBbox.value.width)
    const height = Math.round(drawingBbox.value.height)
    
    return {
      category: props.newBboxCategory,
      size: `${width} × ${height}`,
      isValid: width >= minBboxSize && height >= minBboxSize
    }
  })

  return {
    // 响应式数据
    canDrawing,
    isDrawing,
    drawingBbox,
    startPoint,
    drawingHint,
    minBboxSize,
    shouldStartDrawing,
    
    // 方法
    startDrawing,
    updateDrawing,
    finishDrawing,
    cancelDrawing,
    checkDrawingStatus,
    isBboxElement
  }
}
import { onMounted, onUnmounted } from 'vue'

export function useBboxEvents(bboxResize, bboxDrawing, bboxDrag) {
  // 全局鼠标事件处理
  const handleGlobalMouseMove = (event) => {
    event.preventDefault()
    
    // 优先级：调整大小 > 拖拽 > 绘制
    if (bboxResize.isResizing.value) {
      bboxResize.updateResize(event)
    } else if (bboxDrag.isDragging.value) {
      bboxDrag.updateDrag(event)
    } else if (bboxDrawing.isDrawing.value) {
      // 获取SVG相对坐标
      const rect = event.target.closest('svg')?.getBoundingClientRect()
      if (rect) {
        const svgPoint = {
          x: event.clientX - rect.left,
          y: event.clientY - rect.top
        }
        bboxDrawing.updateDrawing(svgPoint)
      }
    }
  }

  const handleGlobalMouseUp = (event) => {
    if (bboxResize.isResizing.value) {
      bboxResize.finishResize()
    } else if (bboxDrag.isDragging.value) {
      bboxDrag.finishDrag()
    } else if (bboxDrawing.isDrawing.value) {
      bboxDrawing.finishDrawing()
    }
  }

  // 键盘事件处理
  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      // ESC键取消当前操作
      if (bboxResize.isResizing.value) {
        bboxResize.cancelResize()
      } else if (bboxDrag.isDragging.value) {
        bboxDrag.cancelDrag()
      } else if (bboxDrawing.isDrawing.value) {
        bboxDrawing.cancelDrawing()
      }
    }
  }

  // 右键菜单处理
  const handleContextMenu = (event) => {
    if (bboxResize.isResizing.value || bboxDrawing.isDrawing.value || bboxDrag.isDragging.value) {
      event.preventDefault()
    }
  }

  // 阻止默认拖拽行为
  const handleDragStart = (event) => {
    event.preventDefault()
  }

  // 事件监听器管理
  const addEventListeners = () => {
    if (typeof window !== 'undefined') {
      window.addEventListener('mousemove', handleGlobalMouseMove, { passive: false })
      window.addEventListener('mouseup', handleGlobalMouseUp)
      window.addEventListener('keydown', handleKeyDown)
      window.addEventListener('contextmenu', handleContextMenu)
      window.addEventListener('dragstart', handleDragStart)
    }
  }

  const removeEventListeners = () => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('mousemove', handleGlobalMouseMove)
      window.removeEventListener('mouseup', handleGlobalMouseUp)
      window.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('contextmenu', handleContextMenu)
      window.removeEventListener('dragstart', handleDragStart)
    }
  }

  // 生命周期钩子
  onMounted(() => {
    addEventListeners()
  })

  onUnmounted(() => {
    removeEventListeners()
  })

  return {
    // 事件处理方法
    handleGlobalMouseMove,
    handleGlobalMouseUp,
    handleKeyDown,
    handleContextMenu,
    handleDragStart,
    
    // 事件管理方法
    addEventListeners,
    removeEventListeners
  }
}
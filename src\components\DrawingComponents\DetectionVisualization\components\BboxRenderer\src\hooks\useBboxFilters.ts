import { computed } from 'vue'

export function useBboxFilters(props, bboxData) {
  // 类别过滤逻辑
  const getVisibleBboxes = () => {
    if (props.showAllCategories || props.visibleCategories.length === 0) {
      return bboxData.processedBboxes.value
    }
    
    return bboxData.processedBboxes.value.filter(bbox => {
      return props.visibleCategories.includes(bbox.label)
    })
  }

  // 按类别分组
  const bboxesByCategory = computed(() => {
    const groups = {}
    bboxData.processedBboxes.value.forEach(bbox => {
      const category = bbox.label
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(bbox)
    })
    return groups
  })

  // 获取类别统计
  const categoryStats = computed(() => {
    const stats = {}
    bboxData.allCategories.value.forEach(category => {
      const bboxes = bboxesByCategory.value[category] || []
      stats[category] = {
        total: bboxes.length,
        visible: props.visibleCategories.includes(category) || props.showAllCategories,
        color: bboxData.labelColorMap.value[category]
      }
    })
    return stats
  })

  // 切换类别可见性
  const toggleCategoryVisibility = (category) => {
    // 这个需要在父组件中实现，因为需要修改 visibleCategories prop
    // 这里提供辅助方法
    const isVisible = props.visibleCategories.includes(category)
    if (isVisible) {
      return props.visibleCategories.filter(c => c !== category)
    } else {
      return [...props.visibleCategories, category]
    }
  }

  // 显示所有类别
  const showAllCategories = () => {
    return bboxData.allCategories.value
  }

  // 隐藏所有类别
  const hideAllCategories = () => {
    return []
  }

  // 按置信度过滤（如果bbox有confidence字段）
  const filterByConfidence = (minConfidence = 0) => {
    return bboxData.processedBboxes.value.filter(bbox => {
      return !bbox.originalBbox.confidence || bbox.originalBbox.confidence >= minConfidence
    })
  }

  // 按面积过滤
  const filterByArea = (minArea = 0, maxArea = Infinity) => {
    return bboxData.processedBboxes.value.filter(bbox => {
      const area = bbox.width * bbox.height
      return area >= minArea && area <= maxArea
    })
  }

  return {
    // 计算属性
    bboxesByCategory,
    categoryStats,
    
    // 方法
    getVisibleBboxes,
    toggleCategoryVisibility,
    showAllCategories,
    hideAllCategories,
    filterByConfidence,
    filterByArea
  }
}
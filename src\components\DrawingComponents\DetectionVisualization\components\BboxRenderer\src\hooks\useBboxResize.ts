import { ref } from 'vue'

export function useBboxResize(props, emits, bboxChanges) {
  const isResizing = ref(false)
  const resizeData = ref(null)
  const previewBbox = ref(null)

  // 状态跟踪
  const hasActuallyResized = ref(false)

  const { handleTempUpdate } = bboxChanges

  // 控制点位置定义
  const resizeHandles = [
    { position: 'nw', x: 0, y: 0 },
    { position: 'ne', x: 1, y: 0 },
    { position: 'sw', x: 0, y: 1 },
    { position: 'se', x: 1, y: 1 }
  ]

  // 开始调整大小
  const handleResizeStart = (bbox, index, position, event) => {
    if (!props.isEditMode) return
    
    event.stopPropagation()
    event.preventDefault()
    
    isResizing.value = true
    resizeData.value = {
      bbox,
      index,
      position,
      startMouse: { x: event.clientX, y: event.clientY },
      startBbox: { 
        x: bbox.x, 
        y: bbox.y, 
        width: bbox.width, 
        height: bbox.height 
      }
    }
    
    // 初始化预览状态
    previewBbox.value = { ...resizeData.value.startBbox }
  }

  // 更新调整大小
  const updateResize = (mouseEvent) => {
    if (!isResizing.value || !resizeData.value) return
    
    const deltaX = mouseEvent.clientX - resizeData.value.startMouse.x
    const deltaY = mouseEvent.clientY - resizeData.value.startMouse.y
    const { position, startBbox } = resizeData.value
    
    // 检测是否真正发生了调整
    const resizeThreshold = 3
    if (Math.abs(deltaX) > resizeThreshold || Math.abs(deltaY) > resizeThreshold) {
      hasActuallyResized.value = true
    }

    let newBbox = { ...startBbox }
    
    switch (position) {
      case 'nw':
        newBbox.x = startBbox.x + deltaX
        newBbox.y = startBbox.y + deltaY
        newBbox.width = startBbox.width - deltaX
        newBbox.height = startBbox.height - deltaY
        break
      case 'ne':
        newBbox.y = startBbox.y + deltaY
        newBbox.width = startBbox.width + deltaX
        newBbox.height = startBbox.height - deltaY
        break
      case 'sw':
        newBbox.x = startBbox.x + deltaX
        newBbox.width = startBbox.width - deltaX
        newBbox.height = startBbox.height + deltaY
        break
      case 'se':
        newBbox.width = startBbox.width + deltaX
        newBbox.height = startBbox.height + deltaY
        break
    }
    
    // 确保最小尺寸
    const minSize = 10
    if (newBbox.width < minSize) {
      if (position.includes('w')) {
        newBbox.x = startBbox.x + startBbox.width - minSize
      }
      newBbox.width = minSize
    }
    if (newBbox.height < minSize) {
      if (position.includes('n')) {
        newBbox.y = startBbox.y + startBbox.height - minSize
      }
      newBbox.height = minSize
    }
    
    // 立即更新预览状态 - 关键修复
    previewBbox.value = newBbox
  }

  // 完成调整大小 - 修复：正确发送事件
  const finishResize = () => {
    if (!isResizing.value || !resizeData.value || !previewBbox.value) {
      isResizing.value = false
      resizeData.value = null
      previewBbox.value = null
      return
    }
    
    // 转换回原始坐标并发送临时更新事件
    if (hasActuallyResized.value && previewBbox.value && props.reverseTransformPoint) {
      const originalStart = props.reverseTransformPoint({
        x: previewBbox.value.x,
        y: previewBbox.value.y
      })
      const originalEnd = props.reverseTransformPoint({
        x: previewBbox.value.x + previewBbox.value.width,
        y: previewBbox.value.y + previewBbox.value.height
      })
      
      if (originalStart && originalEnd) {
        const updatedBbox = {
          ...resizeData.value.bbox.originalBbox,
          x: Math.min(originalStart.x, originalEnd.x),
          y: Math.min(originalStart.y, originalEnd.y),
          width: Math.abs(originalEnd.x - originalStart.x),
          height: Math.abs(originalEnd.y - originalStart.y)
        }
        
        // 发送临时更新事件
        handleTempUpdate({
          index: resizeData.value.bbox.originalBbox.id,
          bbox: updatedBbox,
          type: 'resize'
        })
      }
    }
    
    // 清理状态
    isResizing.value = false
    resizeData.value = null
    previewBbox.value = null
    hasActuallyResized.value = false
  }

  // 取消调整大小
  const cancelResize = () => {
    isResizing.value = false
    resizeData.value = null
    previewBbox.value = null
    hasActuallyResized.value = false
  }

  // 获取当前调整中的bbox数据
  const getCurrentResizeBbox = (originalBbox) => {
    if (!isResizing.value || !resizeData.value || !previewBbox.value) {
      return null
    }
    
    if (resizeData.value.bbox.originalIndex === originalBbox.originalIndex) {
      return previewBbox.value
    }
    
    return null
  }

  return {
    isResizing,
    resizeData,
    resizeHandles,
    previewBbox,
    hasActuallyResized,
    handleResizeStart,
    updateResize,
    finishResize,
    cancelResize,
    getCurrentResizeBbox
  }
}
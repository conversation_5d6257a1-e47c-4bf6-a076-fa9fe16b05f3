export function useBboxSelect(props, emits, bboxDrag, bboxChanges) {

  const { handleTempUpdate } = bboxChanges
  const { isDragging, dragData, previewDragBbox, hasActuallyDragged } = bboxDrag

  // bbox点击处理
  const handleBboxClick = (bbox, index, event) => {
    event.stopPropagation()
    
    // 如果刚刚完成拖拽，忽略点击事件
    if (hasActuallyDragged.value) {
      hasActuallyDragged.value = false
      return
    }

    if (props.isEditMode) {
      if (props.selectedBboxIndex === bbox.originalIndex) {
        emits('bbox-deselected')
      } else {
        emits('bbox-selected', bbox.originalIndex)
      }
    } else {
      emits('bbox-click', {
        bbox: bbox.originalBbox,
        index: bbox.originalIndex,
        transformedBbox: bbox,
        event,
        coordinates: {
          x: event.offsetX || 0,
          y: event.offsetY || 0
        }
      })
    }
  }

  // bbox鼠标按下处理
  const handleBboxMouseDown = (bbox, index, event) => {
    if (!props.isEditMode || !bbox.isSelected) return
    
    event.stopPropagation()
    event.preventDefault()
    
    isDragging.value = true
    dragData.value = {
      bbox,
      index,
      startMouse: { x: event.clientX, y: event.clientY },
      startBbox: { 
        x: bbox.x, 
        y: bbox.y, 
        width: bbox.width, 
        height: bbox.height 
      }
    }
    
    // 初始化拖拽预览状态
    previewDragBbox.value = { ...dragData.value.startBbox }
    
    // 选中当前bbox
    if (props.selectedBboxIndex !== bbox.originalIndex) {
      emits('bbox-selected', bbox.originalIndex)
    }
  }

  // 删除bbox
  const handleDeleteBbox = (bbox, index, event) => {
    event.stopPropagation()
    event.preventDefault()
    handleTempUpdate({
      index: bbox.originalBbox.id,
      bbox: bbox.originalBbox,
      type: 'delete'
    })
    emits('bbox-deleted', {
      index: bbox.originalBbox.id,
      bbox: bbox.originalBbox
    })
  }

  // 选择bbox
  const selectBbox = (index) => {
    emits('bbox-selected', index)
  }

  // 取消选择bbox
  const deselectBbox = () => {
    emits('bbox-deselected')
  }

  return {
    handleBboxClick,
    handleBboxMouseDown,
    handleDeleteBbox,
    selectBbox,
    deselectBbox,
  }
}
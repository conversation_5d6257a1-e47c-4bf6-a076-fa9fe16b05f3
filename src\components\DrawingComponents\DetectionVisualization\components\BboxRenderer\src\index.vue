<template>
  <g class="bbox-renderer">
    <g v-for="(bbox, index) in filteredProcessedBboxes" :key="`bbox-${index}`">
      <!-- 边界框 -->
      <rect
        :x="getDisplayBbox(bbox).x"
        :y="getDisplayBbox(bbox).y"
        :width="getDisplayBbox(bbox).width"
        :height="getDisplayBbox(bbox).height"
        :stroke="bbox.color"
        :stroke-width="bbox.strokeWidth"
        fill="none"
        class="bbox-rect"
        :class="{ 
          'highlighted': bbox.isHighlighted, 
          'animation': bbox.isAnimation,
          'selected': bbox.isSelected,
          'editable': isEditMode,
          'dragging': isDragging && bbox.isSelected,
          'resizing': isResizing && bbox.isSelected,
          'has-changes': hasChange(bbox.originalIndex)
        }"
        @click="handleBboxClick(bbox, index, $event)"
        @mousedown="handleBboxMouseDown(bbox, index, $event)"
      />
      
      <!-- 更改指示器 -->
      <circle
        v-if="hasChange(bbox.originalBbox.id)"
        :cx="getDisplayBbox(bbox).x + getDisplayBbox(bbox).width - 5"
        :cy="getDisplayBbox(bbox).y + 5"
        r="3"
        fill="#ff9500"
        stroke="white"
        stroke-width="1"
        class="change-indicator"
      />
      
      <!-- 标签背景 -->
      <rect
        v-if="bbox.label && showLabel"
        :x="getDisplayBbox(bbox).x"
        :y="getDisplayBbox(bbox).y - labelHeight"
        :width="bbox.labelWidth"
        :height="labelHeight"
        :fill="bbox.color"
        class="bbox-label-bg"
        :class="{ 
          'highlighted-label': bbox.isHighlighted,
          'animation': bbox.isAnimation,
          'selected-label': bbox.isSelected 
        }"
        @click="handleBboxClick(bbox, index, $event)"
        @mousedown="handleBboxMouseDown(bbox, index, $event)"
      />
      
      <!-- 标签文字 -->
      <text
        v-if="bbox.label && showLabel"
        :x="getDisplayBbox(bbox).x + labelPadding"
        :y="getDisplayBbox(bbox).y - labelPadding"
        fill="white"
        class="bbox-label"
        :class="{ 
          'highlighted-label': bbox.isHighlighted,
          'animation': bbox.isAnimation,
          'selected-label': bbox.isSelected 
        }"
        @click="handleBboxClick(bbox, index, $event)"
        @mousedown="handleBboxMouseDown(bbox, index, $event)"
      >
        {{ displayedLabel(bbox.label) }}
      </text>
      
      <!-- 角点标记 -->
      <g v-if="showCorners" class="corner-points">
        <circle
          v-for="(corner, cornerIndex) in getDisplayCorners(bbox)"
          :key="`corner-${index}-${cornerIndex}`"
          :cx="corner.x"
          :cy="corner.y"
          :r="cornerRadius"
          :fill="bbox.color"
          :stroke="cornerStroke"
          :stroke-width="1"
          class="corner-point"
        />
      </g>

      <!-- 编辑控制点 -->
      <g v-if="isEditMode && bbox.isSelected" class="resize-handles">
        <!-- 四个角的控制点 -->
        <circle
          v-for="(handle, handleIndex) in resizeHandles"
          :key="`handle-${index}-${handleIndex}`"
          :cx="getDisplayBbox(bbox).x + handle.x * getDisplayBbox(bbox).width"
          :cy="getDisplayBbox(bbox).y + handle.y * getDisplayBbox(bbox).height"
          :r="handleRadius"
          :fill="handleColor"
          :stroke="handleStrokeColor"
          :stroke-width="1"
          class="resize-handle"
          :class="[
            `handle-${handle.position}`,
            { 'active': isResizing && bbox.isSelected }
          ]"
          @mousedown.stop="handleResizeStart(bbox, index, handle.position, $event)"
        />
        
        <!-- 删除按钮 -->
        <g 
          class="delete-button"
          @click.stop="handleDeleteBbox(bbox, index, $event)"
        >
          <circle
            :cx="getDisplayBbox(bbox).x + getDisplayBbox(bbox).width + 12"
            :cy="getDisplayBbox(bbox).y - 12"
            :r="10"
            fill="#dc3545"
            stroke="white"
            stroke-width="2"
            class="delete-bg"
          />
          <text
            :x="getDisplayBbox(bbox).x + getDisplayBbox(bbox).width + 12"
            :y="getDisplayBbox(bbox).y - 12 + 4"
            text-anchor="middle"
            fill="white"
            font-size="12"
            font-weight="bold"
            class="delete-icon"
            pointer-events="none"
          >×</text>
        </g>
      </g>
    </g>
    
    <!-- 绘制新bbox的预览 -->
    <rect
      v-if="isEditMode && isDrawing && drawingBbox && shouldStartDrawing"
      :x="drawingBbox.x"
      :y="drawingBbox.y"
      :width="drawingBbox.width"
      :height="drawingBbox.height"
      :stroke="newBboxColor"
      :stroke-width="2"
      fill="none"
      stroke-dasharray="5,5"
      class="drawing-bbox"
      pointer-events="none"
    />
    
    <!-- 绘制时的类别提示 -->
    <g v-if="isEditMode && isDrawing && drawingBbox && shouldStartDrawing" class="drawing-hint">
      <rect
        :x="drawingBbox.x"
        :y="drawingBbox.y - 20"
        :width="Math.max(newBboxCategory.length * 8 + 10, 60)"
        height="16"
        :fill="newBboxColor"
        opacity="0.9"
        rx="2"
      />
      <text
        :x="drawingBbox.x + 5"
        :y="drawingBbox.y - 8"
        fill="white"
        font-size="11"
        font-weight="bold"
      >
        {{ displayedLabel(newBboxCategory) }}
      </text>
    </g>
  </g>
</template>

<script setup>
import { 
  useBboxData,
  useBboxDrawing,
  useBboxResize,
  useBboxDrag,
  useBboxEvents,
  useBboxFilters,
  useBboxChanges,
  useBboxSelect,
} from './hooks'

const props = defineProps({
  // 边界框列表
  bboxList: {
    type: Array,
    default: () => []
  },
  // 坐标转换函数
  transformCoordinate: {
    type: Function,
    required: true
  },
  // 尺寸转换函数
  transformSize: {
    type: Function,
    required: true
  },
  // 反向坐标转换函数
  reverseTransformPoint: {
    type: Function,
    default: null
  },
  // 是否显示标签
  showLabel: {
    type: Boolean,
    default: true
  },
  // 是否显示角点
  showCorners: {
    type: Boolean,
    default: false
  },
  // 样式配置
  strokeWidth: {
    type: Number,
    default: 2
  },
  cornerRadius: {
    type: Number,
    default: 3
  },
  cornerStroke: {
    type: String,
    default: 'white'
  },
  // 高亮配置
  highlightKeys: {
    type: Array,
    default: () => []
  },
  keyField: {
    type: String,
    default: 'id'
  },
  highlightAnimationKeys: {
    type: Array,
    default: () => []
  },
  // 删除标记字段
  deleteField: {
    type: String,
    default: 'isDeleted'
  },
  // 编辑模式
  isEditMode: {
    type: Boolean,
    default: false
  },
  // 选中的bbox索引
  selectedBboxIndex: {
    type: Number,
    default: null
  },
  // 显示的类别过滤
  visibleCategories: {
    type: Array,
    default: () => []
  },
  // 是否显示所有类别
  showAllCategories: {
    type: Boolean,
    default: true
  },
  // 所有类别列表
  allCategories: {
    type: Array,
    default: () => []
  },
  // 类别映射
  categoryMap: {
    type: Object,
    default: () => ({})
  },
  // 新bbox的默认类别
  newBboxCategory: {
    type: String,
    default: 'new'
  },
  // 编辑控制点样式
  handleRadius: {
    type: Number,
    default: 4
  },
  handleColor: {
    type: String,
    default: '#007bff'
  },
  handleStrokeColor: {
    type: String,
    default: 'white'
  }
})

const emits = defineEmits([
  'bbox-click',
  'bbox-selected',
  'bbox-deselected',
  'bbox-updated',
  'bbox-deleted',
  'bbox-temp-updated',
  'bbox-temp-cancel',
  'changes-updated',
  'category-filter-changed'
])

// bbox 数据管理
const bboxData = useBboxData(props), {
  labelHeight,
  labelPadding,
  allCategories,
  newBboxColor,
  processedBboxes,
  filteredProcessedBboxes,
  displayedLabel,
  processBboxes
} = bboxData

// bbox 人工调整
const bboxChanges = useBboxChanges(emits), {
  hasUnsavedChanges,
  changeStats,
  addTempChange,
  clearAllChanges,
  getAllChanges,
  hasChange,
  handleTempUpdate,
  saveAllChanges,
  cancelAllChanges
} = bboxChanges

// bbox 绘制管理
const bboxDrawing = useBboxDrawing(props, emits, bboxData, bboxChanges), {
  isDrawing,
  canDrawing,
  drawingBbox,
  shouldStartDrawing,
  startDrawing,
  updateDrawing,
  finishDrawing,
  cancelDrawing,
  isBboxElement
} = bboxDrawing

// bbox 编辑
const bboxResize = useBboxResize(props, emits, bboxChanges), {
  isResizing,
  resizeHandles,
  handleResizeStart,
  getCurrentResizeBbox
} = bboxResize

// bbox 交互
const bboxDrag = useBboxDrag(props, emits, bboxChanges), {
  isDragging,
  getCurrentDragBbox
} = bboxDrag

const bboxSelect = useBboxSelect(props, emits, bboxDrag, bboxChanges), {
  handleBboxClick,
  handleBboxMouseDown,
  handleDeleteBbox,
  selectBbox,
  deselectBbox,
} = bboxSelect

// bbox 类别过滤
const bboxFilters = useBboxFilters(props, bboxData), {
  bboxesByCategory,
  categoryStats,
  getVisibleBboxes,
} = bboxFilters

// 设置全局事件监听
useBboxEvents(bboxResize, bboxDrawing, bboxDrag)


// 获取显示用的bbox数据（考虑调整大小时的实时预览）
const getDisplayBbox = (bbox) => {
  // 优先检查调整大小状态
  if (isResizing.value && bbox.isSelected) {
    const resizeBbox = getCurrentResizeBbox(bbox)
    if (resizeBbox) {
      return resizeBbox
    }
  }
  
  // 然后检查拖拽状态
  if (isDragging.value && bbox.isSelected) {
    const dragBbox = getCurrentDragBbox(bbox)
    if (dragBbox) {
      return dragBbox
    }
  }
  
  return bbox
}

// 获取显示用的角点数据
const getDisplayCorners = (bbox) => {
  const displayBbox = getDisplayBbox(bbox)
  return [
    { x: displayBbox.x, y: displayBbox.y },
    { x: displayBbox.x + displayBbox.width, y: displayBbox.y },
    { x: displayBbox.x + displayBbox.width, y: displayBbox.y + displayBbox.height },
    { x: displayBbox.x, y: displayBbox.y + displayBbox.height }
  ]
}

// 暴露方法和数据
defineExpose({
  // 数据
  processedBboxes,
  filteredProcessedBboxes,
  allCategories,
  
  // 数据处理方法
  processBboxes,
  
  // 绘制方法
  startDrawing,
  updateDrawing,
  finishDrawing,
  cancelDrawing,
  isDrawing,
  canDrawing,
  drawingBbox,
  
  // 过滤方法
  getVisibleBboxes,
  bboxesByCategory,
  categoryStats,
  
  // 交互方法
  selectBbox,
  deselectBbox,

  // 更改管理
  hasUnsavedChanges,
  changeStats,
  handleTempUpdate,
  saveAllChanges,
  cancelAllChanges,
  getAllChanges
})
</script>

<style scoped lang="scss">
/* 边界框样式 */
.bbox-rect {
  cursor: pointer;
  transition: stroke 0.2s ease, stroke-width 0.2s ease, filter 0.2s ease, opacity 0.2s ease;
}

/* 标签背景 */
.bbox-label-bg {
  cursor: pointer;
  transition: fill 0.2s ease, filter 0.2s ease, opacity 0.2s ease;
}

/* 标签文字 */
.bbox-label {
  font-size: 0.6rem;
  font-weight: bold;
  pointer-events: none;
  user-select: none;
  transition: fill 0.2s ease, filter 0.2s ease, font-size 0.2s ease, opacity 0.2s ease;
}

/* 高亮动画（呼吸脉冲）*/
@keyframes pulse-glow {
  0%, 100% {
    filter: drop-shadow(0 0 12px currentColor);
    transform: scale(1);
  }
  50% {
    filter: drop-shadow(0 0 22px currentColor);
    transform: scale(1.06);
  }
}

/* 高亮样式 */
.bbox-rect.highlighted {
  stroke: #FFD700 !important;
  stroke-width: 4px !important;
  color: #FFD700;
  opacity: 1 !important;
  z-index: 10;
  &.animation {
    animation: pulse-glow 1.2s ease-in-out infinite;
  }
}

.bbox-label-bg.highlighted-label {
  fill: #FFD700 !important;
  color: #FFD700;
  opacity: 1 !important;
  &.animation {
    animation: pulse-glow 1.2s ease-in-out infinite;
  }
}

.bbox-label.highlighted-label {
  fill: #fff !important;
  font-size: 0.7rem;
  color: #FFD700;
  opacity: 1 !important;
  &.animation {
    animation: pulse-glow 1.2s ease-in-out infinite;
  }
}

/* 选中样式 */
.bbox-rect.selected {
  stroke: #007bff !important;
  stroke-width: 3px !important;
  color: #007bff;
  opacity: 1 !important;
}

.bbox-label-bg.selected-label {
  fill: #007bff !important;
  color: #007bff;
  opacity: 1 !important;
}

.bbox-label.selected-label {
  fill: #fff !important;
  font-size: 0.7rem;
  color: #007bff;
  opacity: 1 !important;
}

.bbox-rect.editable {
  cursor: move;
}

.bbox-rect.dragging {
  cursor: grabbing;
  stroke-width: 2px !important;
  opacity: 0.8;
}

/* 有更改的bbox样式 */
.bbox-rect.has-changes {
  stroke-dasharray: 2,2;
  stroke-width: 2px !important;
}

/* 调整大小时的样式 */
.bbox-rect.resizing {
  stroke-dasharray: 8,2;
  opacity: 0.8;
}

.bbox-rect:hover {
  stroke-width: 2px;
}

.corner-point {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.corner-point:hover {
  opacity: 1;
}

/* 编辑控制层 */
.edit-controls-layer {
  pointer-events: all;
  z-index: 100;
}

/* 调整大小控制点 */
.resize-handles {
  pointer-events: all;
}

.resize-handle {
  cursor: pointer;
  
  &:hover {
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
  }
  
  &.active {
    filter: drop-shadow(0 2px 6px rgba(0,0,0,0.4));
    transition: none;
  }
}


.handle-nw, .handle-se {
  cursor: nw-resize;
}

.handle-ne, .handle-sw {
  cursor: ne-resize;
}

/* 删除按钮 */
.delete-button {
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-button:hover .delete-bg {
  fill: #c82333;
}

.delete-icon {
  pointer-events: none;
  user-select: none;
}

/* 绘制中的bbox */
.drawing-bbox {
  opacity: 0.7;
  pointer-events: none;
}

/* 绘制提示 */
.drawing-hint {
  pointer-events: none;
  opacity: 0.9;
}
</style>
<template>
  <g class="leader-line-component">
    <!-- 引线 -->
    <line
      :x1="startPoint.x"
      :y1="startPoint.y"
      :x2="endPoint.x"
      :y2="endPoint.y"
      :stroke="color"
      :stroke-width="strokeWidth"
      :stroke-dasharray="dashArray"
      class="leader-line"
      @click="handleLineClick"
    />
    
    <!-- 起始点标记（最近角点） -->
    <circle
      :cx="startPoint.x"
      :cy="startPoint.y"
      :r="startPointRadius"
      :fill="color"
      :stroke="startPointStroke"
      :stroke-width="2"
      class="start-point"
      @click="handleStartPointClick"
    />
    
    <!-- 终点标记（目标点） -->
    <circle
      :cx="endPoint.x"
      :cy="endPoint.y"
      :r="endPointRadius"
      :fill="endPointFill"
      :stroke="color"
      :stroke-width="2"
      class="end-point"
      @click="handleEndPointClick"
    />
    
    <!-- 坐标标签 -->
    <g v-if="showCoordinates" class="coordinate-label">
      <rect
        :x="labelPosition.x"
        :y="labelPosition.y"
        :width="labelWidth"
        :height="labelHeight"
        :fill="labelBg"
        :stroke="color"
        :stroke-width="1"
        rx="3"
        class="coordinate-label-bg"
      />
      <text
        :x="labelPosition.x + 5"
        :y="labelPosition.y + labelHeight - 5"
        :fill="labelTextColor"
        class="coordinate-text"
      >
        {{ coordinateText }}
      </text>
    </g>
    
    <!-- 删除按钮 -->
    <g v-if="showDeleteButton" class="delete-button" @click="handleDelete">
      <circle
        :cx="endPoint.x + 15"
        :cy="endPoint.y + 10"
        r="8"
        fill="#ff4757"
        stroke="white"
        stroke-width="2"
        class="delete-button-bg"
      />
      <text
        :x="endPoint.x + 15"
        :y="endPoint.y + 13"
        fill="white"
        text-anchor="middle"
        class="delete-button-text"
      >
        ×
      </text>
    </g>
  </g>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 起始点（角点）
  startPoint: {
    type: Object,
    required: true,
    validator: (value) => value && typeof value.x === 'number' && typeof value.y === 'number'
  },
  // 终点（目标点）
  endPoint: {
    type: Object,
    required: true,
    validator: (value) => value && typeof value.x === 'number' && typeof value.y === 'number'
  },
  // 原始坐标（用于显示）
  originalCoordinate: {
    type: Object,
    required: true
  },
  // 颜色
  color: {
    type: String,
    default: '#FF6B6B'
  },
  // 样式配置
  strokeWidth: {
    type: Number,
    default: 2
  },
  dashArray: {
    type: String,
    default: '3,3'
  },
  startPointRadius: {
    type: Number,
    default: 4
  },
  endPointRadius: {
    type: Number,
    default: 5
  },
  startPointStroke: {
    type: String,
    default: 'white'
  },
  endPointFill: {
    type: String,
    default: '#FFE4E1'
  },
  // 标签配置
  showCoordinates: {
    type: Boolean,
    default: true
  },
  coordsType: {
    type: String,
    default: 'absolute'
  },
  labelBg: {
    type: String,
    default: 'rgba(255, 255, 255, 0.9)'
  },
  labelTextColor: {
    type: String,
    default: '#333'
  },
  // 交互配置
  showDeleteButton: {
    type: Boolean,
    default: false
  },
  // 数据
  leaderData: {
    type: Object,
    default: () => ({})
  }
})

const emits = defineEmits(['line-click', 'start-point-click', 'end-point-click', 'delete'])

// 样式常量
const labelHeight = 20

// 计算坐标文本
const coordinateText = computed(() => {
  const coord = props.originalCoordinate
  if (props.coordsType === 'relative') {
    return `(${coord.x.toFixed(3)}, ${coord.y.toFixed(3)})`
  } else {
    return `(${Math.round(coord.x)}, ${Math.round(coord.y)})`
  }
})

// 计算标签宽度
const labelWidth = computed(() => {
  return coordinateText.value.length * 7 + 10
})

// 计算标签位置
const labelPosition = computed(() => {
  const offset = 10
  let x = props.endPoint.x + offset
  let y = props.endPoint.y - labelHeight
  
  // 简单的边界检查，避免标签超出视图
  // 这里可以根据实际需要调整
  return { x, y }
})

// 事件处理
const handleLineClick = (event) => {
  event.stopPropagation()
  emits('line-click', {
    leaderData: props.leaderData,
    event
  })
}

const handleStartPointClick = (event) => {
  event.stopPropagation()
  emits('start-point-click', {
    leaderData: props.leaderData,
    point: props.startPoint,
    event
  })
}

const handleEndPointClick = (event) => {
  event.stopPropagation()
  emits('end-point-click', {
    leaderData: props.leaderData,
    point: props.endPoint,
    event
  })
}

const handleDelete = (event) => {
  event.stopPropagation()
  emits('delete', {
    leaderData: props.leaderData,
    event
  })
}
</script>

<style scoped>
.leader-line {
  cursor: pointer;
  transition: stroke-width 0.2s ease;
}

.leader-line:hover {
  stroke-width: 3px;
}

.start-point,
.end-point {
  cursor: pointer;
  transition: r 0.2s ease;
}

.start-point:hover,
.end-point:hover {
  r: 6;
}

.coordinate-label-bg {
  filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.2));
}

.coordinate-text {
  font-size: 11px;
  font-weight: bold;
  pointer-events: none;
  user-select: none;
}

.delete-button {
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.delete-button:hover {
  opacity: 1;
}

.delete-button-text {
  font-size: 12px;
  font-weight: bold;
  pointer-events: none;
  user-select: none;
}
</style>
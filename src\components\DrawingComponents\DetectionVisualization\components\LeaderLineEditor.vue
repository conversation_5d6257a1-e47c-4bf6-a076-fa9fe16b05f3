<template>
  <g class="leader-line-editor">
    <!-- 高亮选中的bbox -->
    <g v-if="selectedBboxIndex !== null && bboxList[selectedBboxIndex]" class="selected-bbox-highlight">
      <rect
        :x="bboxList[selectedBboxIndex].x - 2"
        :y="bboxList[selectedBboxIndex].y - 2"
        :width="bboxList[selectedBboxIndex].width + 4"
        :height="bboxList[selectedBboxIndex].height + 4"
        fill="none"
        :stroke="selectedBboxColor"
        :stroke-width="3"
        stroke-dasharray="8,4"
        class="selected-bbox-border"
      />
      
      <!-- 选中bbox的角点高亮 -->
      <g v-if="bboxList[selectedBboxIndex].corners">
        <circle
          v-for="(corner, cornerIndex) in bboxList[selectedBboxIndex].corners"
          :key="`selected-corner-${cornerIndex}`"
          :cx="corner.x"
          :cy="corner.y"
          :r="selectedCornerRadius"
          :fill="selectedBboxColor"
          :stroke="'white'"
          :stroke-width="2"
          class="selected-corner"
        />
      </g>
    </g>
    
    <!-- 不可选择的bbox遮罩 -->
    <g v-if="isActive && maxLeadersPerBbox > 0">
      <rect
        v-for="(bbox, idx) in filteredBboxList"
        :key="`bbox-mask-${idx}`"
        :x="bbox.x"
        :y="bbox.y"
        :width="bbox.width"
        :height="bbox.height"
        fill="rgba(0, 0, 0, 0.3)"
        stroke="rgba(255, 0, 0, 0.5)"
        stroke-width="2"
        stroke-dasharray="4,4"
        class="bbox-disabled-mask"
      />
    </g>
    
    <!-- 实时预览引线 -->
    <g v-if="isActive && selectedBboxIndex !== null && currentMousePos && previewData">
      <line
        :x1="previewData.startPoint.x"
        :y1="previewData.startPoint.y"
        :x2="previewData.endPoint.x"
        :y2="previewData.endPoint.y"
        :stroke="previewColor"
        :stroke-width="previewStrokeWidth"
        :stroke-dasharray="previewDashArray"
        class="preview-line"
      />
      
      <!-- 预览起始点 -->
      <circle
        :cx="previewData.startPoint.x"
        :cy="previewData.startPoint.y"
        :r="previewStartRadius"
        :fill="previewColor"
        :stroke="previewStartStroke"
        :stroke-width="2"
        class="preview-start-point"
      />
      
      <!-- 预览终点 -->
      <circle
        :cx="previewData.endPoint.x"
        :cy="previewData.endPoint.y"
        :r="previewEndRadius"
        :fill="previewEndFill"
        :stroke="previewColor"
        :stroke-width="1"
        class="preview-end-point"
      />
      
      <!-- 预览坐标标签 -->
      <g v-if="showPreviewCoordinates" class="preview-coordinate-label">
        <rect
          :x="previewData.endPoint.x + 10"
          :y="previewData.endPoint.y - 15"
          :width="previewLabelWidth"
          :height="20"
          :fill="previewLabelBg"
          :stroke="previewColor"
          :stroke-width="1"
          rx="3"
          class="preview-coordinate-label-bg"
        />
        <text
          :x="previewData.endPoint.x + 15"
          :y="previewData.endPoint.y - 2"
          :fill="previewLabelTextColor"
          class="preview-coordinate-text"
        >
          {{ previewCoordinateText }}
        </text>
      </g>
    </g>
    
  </g>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 是否激活编辑模式
  isActive: {
    type: Boolean,
    default: false
  },
  // 当前选中的bbox索引
  selectedBboxIndex: {
    type: Number,
    default: null
  },
  // 当前鼠标位置
  currentMousePos: {
    type: Object,
    default: null
  },
  // 边界框列表（已处理过的）
  bboxList: {
    type: Array,
    default: () => []
  },
  // 坐标转换函数
  reverseTransformPoint: {
    type: Function,
    required: true
  },
  // 坐标类型
  coordsType: {
    type: String,
    default: 'absolute'
  },
  // 选中bbox样式
  selectedBboxColor: {
    type: String,
    default: '#FFD700'
  },
  selectedCornerRadius: {
    type: Number,
    default: 5
  },
  // 预览样式
  previewColor: {
    type: String,
    default: '#FF6B6B'
  },
  previewStrokeWidth: {
    type: Number,
    default: 2
  },
  previewDashArray: {
    type: String,
    default: '5,5'
  },
  previewStartRadius: {
    type: Number,
    default: 4
  },
  previewEndRadius: {
    type: Number,
    default: 3
  },
  previewStartStroke: {
    type: String,
    default: 'white'
  },
  previewEndFill: {
    type: String,
    default: '#FFE4E1'
  },
  // 标签配置
  showPreviewCoordinates: {
    type: Boolean,
    default: true
  },
  previewLabelBg: {
    type: String,
    default: 'rgba(255, 255, 255, 0.9)'
  },
  previewLabelTextColor: {
    type: String,
    default: '#333'
  },
  bboxLeaderCounts: {
    type: Object,
    default: () => ({})
  },
  maxLeadersPerBbox: {
    type: Number,
    default: 0
  }
})

const filteredBboxList = computed(() => {
  return props.bboxList.filter((bbox, idx) => {
    return !canBboxAddLeader(idx) && props.selectedBboxIndex !== idx
  })
})

// 计算点到点的距离
const getDistance = (p1, p2) => {
  const dx = p1.x - p2.x
  const dy = p1.y - p2.y
  return Math.sqrt(dx * dx + dy * dy)
}

// 找到最近的角点（仅针对选中的bbox）
const findNearestCornerInBbox = (targetPoint, bbox) => {
  if (!targetPoint || !bbox || !bbox.corners) return null
  
  let nearestCorner = bbox.corners[0]
  let minDistance = getDistance(targetPoint, nearestCorner)
  
  for (let i = 1; i < bbox.corners.length; i++) {
    const distance = getDistance(targetPoint, bbox.corners[i])
    if (distance < minDistance) {
      minDistance = distance
      nearestCorner = bbox.corners[i]
    }
  }
  
  return nearestCorner
}

// 检查bbox是否可以添加引线
const canBboxAddLeader = (bboxIndex) => {
  if (props.maxLeadersPerBbox === 0) return true
  const count = props.bboxLeaderCounts[bboxIndex] || 0
  return count < props.maxLeadersPerBbox
}

// 预览数据
const previewData = computed(() => {
  if (!props.isActive || 
      props.selectedBboxIndex === null || 
      !props.currentMousePos || 
      !props.bboxList[props.selectedBboxIndex]) {
    return null
  }
  
  const selectedBbox = props.bboxList[props.selectedBboxIndex]
  const nearestCorner = findNearestCornerInBbox(props.currentMousePos, selectedBbox)
  
  if (!nearestCorner) return null
  
  return {
    startPoint: nearestCorner,
    endPoint: props.currentMousePos
  }
})

// 预览坐标文本
const previewCoordinateText = computed(() => {
  if (!previewData.value) return ''
  
  const originalPoint = props.reverseTransformPoint(previewData.value.endPoint)
  if (!originalPoint) return ''
  
  if (props.coordsType === 'relative') {
    return `(${originalPoint.x.toFixed(3)}, ${originalPoint.y.toFixed(3)})`
  } else {
    return `(${Math.round(originalPoint.x)}, ${Math.round(originalPoint.y)})`
  }
})

// 预览标签宽度
const previewLabelWidth = computed(() => {
  return previewCoordinateText.value.length * 7 + 10
})
</script>

<style scoped>
.selected-bbox-border {
  animation: dash 2s linear infinite;
  pointer-events: none;
}

@keyframes dash {
  to {
    stroke-dashoffset: -24;
  }
}

.selected-corner {
  animation: pulse-corner 1s ease-in-out infinite alternate;
  pointer-events: none;
}

@keyframes pulse-corner {
  from { 
    r: 5;
    opacity: 0.8;
  }
  to { 
    r: 7;
    opacity: 1;
  }
}

.bbox-disabled-mask {
  pointer-events: none;
  animation: disabled-blink 2s ease-in-out infinite;
}

@keyframes disabled-blink {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.5; }
}

.preview-line {
  opacity: 0.8;
  animation: pulse 1s ease-in-out infinite alternate;
  pointer-events: none;
}

@keyframes pulse {
  from { opacity: 0.6; }
  to { opacity: 1; }
}

.preview-coordinate-label-bg {
  filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.2));
  pointer-events: none;
}

.preview-coordinate-text {
  font-size: 11px;
  font-weight: bold;
  pointer-events: none;
  user-select: none;
}
</style>
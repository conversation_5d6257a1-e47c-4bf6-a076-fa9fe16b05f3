import { ref, computed, watch, nextTick } from 'vue'

export function useAnomalyDetection(props, emits) {

  const handleAnomalySelected = (anomaly) => {
    emits('anomaly-selected', anomaly)
  }

  const handleAnomalyHighlighted = (anomaly) => {
    emits('anomaly-highlighted', anomaly)
  }

  const handleAnomalyTypeFilterChanged = (selectedAnomalyTypes) => {
    emits('anomaly-filter-changed', selectedAnomalyTypes)
  }  

  return {
    handleAnomalySelected,
    handleAnomalyHighlighted,
    handleAnomalyTypeFilterChanged
  }
}
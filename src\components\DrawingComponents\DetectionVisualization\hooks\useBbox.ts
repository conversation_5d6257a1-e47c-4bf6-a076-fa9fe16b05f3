import { ref, computed } from 'vue'

export function useBbox(props, emit, bboxRendererRef) {

  // 边界框状态
  const selectedBboxIndex = ref(null)

  // 处理后的边界框
  const processedBboxes = computed(() => {
    return bboxRendererRef.value?.processedBboxes || []
  })

  const changeStats = computed(() => {
    return bboxRendererRef.value?.changeStats || {}
  })

  const newBboxCategory = computed(() => {
    const visibleCategories = props.visibleCategories
    return visibleCategories && visibleCategories[0] || 'new'
  })

  // 边界框事件处理
  const handleBboxClick = (data) => {
    emit('bbox-click', data)
  }

  const handleBboxSelected = (index) => {
    selectedBboxIndex.value = index
    emit('bbox-selected', { index })
  }

  const handleBboxDeselected = () => {
    selectedBboxIndex.value = null
  }

  const handleBboxUpdated = (data) => {
    emit('bbox-updated', data)
  }

  const handleBboxDeleted = (data) => {
    emit('bbox-deleted', data)
    selectedBboxIndex.value = null
  }

  const handleBboxTempUpdated = (data) => {
    emit('bbox-temp-updated', data)
  }

  const handleBboxTempCancel = () => {
    emit('bbox-temp-cancel')
    resetBboxSelection()
  }

  // 重置选择状态
  const resetBboxSelection = () => {
    selectedBboxIndex.value = null
  }

  // 取消选择
  const cancelSelection = () => {
    selectedBboxIndex.value = null
  }

  // 是否可以绘制新的bbox
  const canDrawing = computed(() => {
    return bboxRendererRef.value?.canDrawing || false
  })

  // 保存bbox更改
  const saveBboxChanges = () => {
    bboxRendererRef.value?.saveAllChanges()
  }

  // 取消bbox更改
  const cancelBboxChanges = () => {
    bboxRendererRef.value?.cancelAllChanges()
  }

  return {
    // 状态
    canDrawing,
    selectedBboxIndex,
    bboxRendererRef,
    processedBboxes,
    newBboxCategory,
    changeStats,
    
    // 方法
    handleBboxClick,
    handleBboxSelected,
    handleBboxDeselected,
    handleBboxUpdated,
    handleBboxDeleted,
    handleBboxTempUpdated,
    handleBboxTempCancel,
    resetBboxSelection,
    cancelSelection,
    saveBboxChanges,
    cancelBboxChanges
  }
}
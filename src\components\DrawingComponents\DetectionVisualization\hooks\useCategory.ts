export function useCategory(props, emit) {
  // 类别过滤相关方法
  const toggleCategory = (category) => {
    emit('category-changed', { category, action: 'toggle' })
  }

  const showOnlyCategory = (category) => {
    emit('category-changed', { category, action: 'show-only' })
  }

  const showAllBboxCategories = () => {
    emit('category-changed', { action: 'show-all' })
  }

  return {
    // 方法
    toggleCategory,
    showOnlyCategory,
    showAllBboxCategories
  }
}
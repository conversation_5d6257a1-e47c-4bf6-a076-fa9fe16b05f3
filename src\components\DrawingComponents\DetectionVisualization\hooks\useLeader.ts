import { ref, computed, nextTick } from 'vue'

export function useLeader(props, emit, bboxHook, viewerHook) {

  // 引线状态
  const curEndpointPos = ref(null)

  // 计算每个bbox的引线数量
  const bboxLeaderCounts = computed(() => {
    const counts = {}
    props.leaders.forEach(leader => {
      if (typeof leader.bboxIndex === 'number') {
        counts[leader.bboxIndex] = (counts[leader.bboxIndex] || 0) + 1
      }
    })
    return counts
  })

  // 检查是否可以添加更多引线
  const canAddMoreLeaders = computed(() => {
    // 检查总数限制
    if (props.maxTotalLeaders > 0 && props.leaders.length >= props.maxTotalLeaders) {
      return false
    }
    
    // 如果没有选中bbox，检查是否有bbox可以添加引线
    if (bboxHook.selectedBboxIndex.value === null) {
      if (props.maxLeadersPerBbox === 0) return true
      
      return bboxHook.processedBboxes.value.some((_, index) => {
        const count = bboxLeaderCounts.value[index] || 0
        return count < props.maxLeadersPerBbox
      })
    }
    
    // 检查选中bbox的限制
    if (props.maxLeadersPerBbox > 0) {
      const count = bboxLeaderCounts.value[bboxHook.selectedBboxIndex.value] || 0
      return count < props.maxLeadersPerBbox
    }
    
    return true
  })

  // 检查特定bbox是否可以添加引线
  const canBboxAddLeader = (bboxIndex) => {
    if (props.maxLeadersPerBbox === 0) return true
    const count = bboxLeaderCounts.value[bboxIndex] || 0
    return count < props.maxLeadersPerBbox
  }

  // 计算点到点的距离
  const getDistance = (p1, p2) => {
    const dx = p1.x - p2.x
    const dy = p1.y - p2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  // 找到最近的角点
  const findNearestCornerInBbox = (targetPoint, bbox) => {
    if (!targetPoint || !bbox || !bbox.corners) return null
    
    let nearestCorner = bbox.corners[0]
    let minDistance = getDistance(targetPoint, nearestCorner)
    
    for (let i = 1; i < bbox.corners.length; i++) {
      const distance = getDistance(targetPoint, bbox.corners[i])
      if (distance < minDistance) {
        minDistance = distance
        nearestCorner = bbox.corners[i]
      }
    }
    
    return nearestCorner
  }

  // 处理后的引线
  const processedLeaders = computed(() => {
    if (!Array.isArray(props.leaders) || props.leaders.length === 0) {
      return []
    }
    
    const viewer = viewerHook.imageViewerRef.value
    if (!viewer) return []
    
    return props.leaders
      .filter(leader => 
        leader && 
        leader.bboxId &&
        leader.targetPoint &&
        typeof leader.targetPoint.x === 'number' &&
        typeof leader.targetPoint.y === 'number'
      )
      .map((leader, index) => {
        const bbox = bboxHook.processedBboxes.value.find(b => b.originalBbox.id === leader.bboxId)
        if (!bbox) return null
        
        const targetPoint = viewer.transformPoint(leader.targetPoint)
        if (!targetPoint) return null
        
        const nearestCorner = findNearestCornerInBbox(targetPoint, bbox)
        if (!nearestCorner) return null
        
        return {
          bboxId: leader.bboxId,
          startPoint: nearestCorner,
          endPoint: targetPoint,
          originalCoordinate: leader.targetPoint,
          color: leader.color || bbox.color,
          originalIndex: index,
          originalLeader: leader,
          bboxIndex: leader.bboxIndex
        }
      })
      .filter(Boolean)
  })

  // 添加引线
  const addLeaderAtPosition = (position) => {
    const viewer = viewerHook.imageViewerRef.value
    if (!viewer || bboxHook.selectedBboxIndex.value === null) return
    
    // 再次检查是否可以添加引线
    if (!canAddMoreLeaders.value || !canBboxAddLeader(bboxHook.selectedBboxIndex.value)) {
      return
    }
    
    const originalPoint = viewer.reverseTransformPoint(position)
    if (!originalPoint) return
    
    const selectedBbox = bboxHook.processedBboxes.value[bboxHook.selectedBboxIndex.value]
    if (!selectedBbox) return

    const startPoint = findNearestCornerInBbox(position, selectedBbox)
    const endPoint = position
    
    const newLeader = {
      bboxId: selectedBbox.originalBbox.id,
      bboxIndex: bboxHook.selectedBboxIndex.value,
      startPoint,
      endPoint,
      originalCoordinate: originalPoint,
      targetPoint: originalPoint,
      color: selectedBbox.color || '#FF6B6B'
    }
    
    // 如果该bbox还能添加更多引线，保持选中状态，否则取消选择
    const currentCount = bboxLeaderCounts.value[bboxHook.selectedBboxIndex.value] || 0
    if (props.maxLeadersPerBbox > 0 && currentCount + 1 >= props.maxLeadersPerBbox) {
      bboxHook.selectedBboxIndex.value = null
    }
    
    resetLeaderStates()

    emit('leader-added', newLeader)
  }

  // 引线事件处理
  const handleLeaderClick = (leader) => {
    emit('leader-click', leader)
  }

  const handleLeaderDelete = (leaderObj) => {
    const index = leaderObj.leaderData?.originalIndex
    if (typeof index === 'number') {
      emit('leader-removed', { index, leader: leaderObj.leaderData?.originalLeader })
    }
  }

  // 清除所有引线
  const clearAllLeaders = () => {
    emit('leader-removed', 'all')
  }

  // 构建引线数据
  const buildLeaderData = (point, bboxId, bboxList) => {
    const viewer = viewerHook.imageViewerRef.value
    if (!viewer) return null

    const bboxRenderer = bboxHook.bboxRendererRef.value
    if (!bboxRenderer) return null
    
    const position = viewer.transformPoint(point)
    if (!position) return null

    const processedBboxes = bboxRenderer.processBboxes(bboxList)
    const bbox = processedBboxes.find(b => b.originalBbox.id === bboxId)
    if (!bbox) return null
    
    const startPoint = findNearestCornerInBbox(position, bbox)
    if (!startPoint) return null
    
    return {
      bboxId: bbox.originalBbox.id,
      bboxIndex: processedBboxes.indexOf(bbox),
      startPoint,
      endPoint: position,
      originalCoordinate: point,
      targetPoint: point,
      color: bbox.color || '#FF6B6B'
    }
  }

  const setEndpointPos = (position) => {
    curEndpointPos.value = position
  }

  // 重置引线状态
  const resetLeaderStates = () => {
    curEndpointPos.value = null
  }

  return {
    // 状态
    curEndpointPos,
    bboxLeaderCounts,
    canAddMoreLeaders,
    processedLeaders,
    
    // 方法
    canBboxAddLeader,
    findNearestCornerInBbox,
    addLeaderAtPosition,
    handleLeaderClick,
    handleLeaderDelete,
    clearAllLeaders,
    buildLeaderData,
    resetLeaderStates,
    setEndpointPos
  }
}
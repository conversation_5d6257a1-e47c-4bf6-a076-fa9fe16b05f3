import { set } from '@vueuse/core'
import { ref, computed } from 'vue'

export function useMode(props, emit) {

  // 模式状态
  const isPreviewMode = ref(true)
  const isLeaderEditMode = ref(false)
  const isBboxEditMode = ref(false)

  // 外部重置函数列表
  let externalResets = []

  const setExternalResets = (resets) => {
    externalResets = resets
  }

  // 计算当前模式
  const currentMode = computed(() => {
    if (isPreviewMode.value) return 'preview'
    if (isLeaderEditMode.value) return 'leader-edit'
    if (isBboxEditMode.value) return 'bbox-edit'
    return 'unknown'
  })

  // 重置编辑状态
  const resetEditStates = () => {
    externalResets.forEach(fn => fn && fn())
  }

  // 模式切换方法
  const setPreviewMode = () => {
    isPreviewMode.value = true
    isLeaderEditMode.value = false
    isBboxEditMode.value = false
    resetEditStates()
    emit('mode-changed', 'preview')
  }

  const setLeaderEditMode = () => {
    isPreviewMode.value = false
    isLeaderEditMode.value = true
    isBboxEditMode.value = false
    resetEditStates()
    emit('mode-changed', 'leader-edit')
  }

  const setBboxEditMode = () => {
    isPreviewMode.value = false
    isLeaderEditMode.value = false
    isBboxEditMode.value = true
    resetEditStates()
    emit('mode-changed', 'bbox-edit')
  }

  // 初始化默认模式
  const initializeMode = () => {
    switch(props.defaultMode) {
      case 'leader-edit':
        setLeaderEditMode()
        break
      case 'bbox-edit':
        setBboxEditMode()
        break
      default:
        setPreviewMode()
    }
  }

  return {
    // 状态
    isPreviewMode,
    isLeaderEditMode,
    isBboxEditMode,
    currentMode,
    
    // 方法
    setPreviewMode,
    setLeaderEditMode,
    setBboxEditMode,
    resetEditStates,
    initializeMode,
    setExternalResets
  }
}
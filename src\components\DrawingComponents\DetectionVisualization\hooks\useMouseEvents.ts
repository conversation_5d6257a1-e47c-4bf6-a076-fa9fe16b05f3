import { set } from '@vueuse/core'
import { ref, nextTick } from 'vue'

export function useMouseEvents(props, emit, modeHook, bboxHook, leaderHook, viewerHook) {

  const { setEndpointPos } = leaderHook

  // 鼠标状态
  const isMouseDown = ref(false)
  const lastMousePos = ref(null)

  // 鼠标滚轮事件处理（缩放）
  const handleWheel = ({ event }) => {
    // if (!modeHook.isPreviewMode.value || !props.enableZoom) {
    //   event.stopPropagation()
    //   return
    // }
    
    emit('zoom-changed', { event })
  }

  // 鼠标移动事件处理
  const handleMouseMove = (event) => {
    emit('mouse-move', event)

    const { containerCoords: absolute, wrapperCoords: relative } = event
    if (modeHook.isPreviewMode.value && props.enablePan) {
      if (isMouseDown.value && lastMousePos.value) {
        const deltaX = absolute.x - lastMousePos.value.x
        const deltaY = absolute.y - lastMousePos.value.y
        
        viewerHook.adjustPanOffset(deltaX, deltaY)
        
        emit('pan-changed', { 
          offset: viewerHook.imageViewerRef.value?.panOffset, 
          delta: { x: deltaX, y: deltaY } 
        })
      }
      lastMousePos.value = absolute
      return
    }
    
    if (modeHook.isBboxEditMode.value && props.enableBboxEdit) {
      // bbox编辑模式下更新绘制
      if (bboxHook.bboxRendererRef.value?.isDrawing) {
        bboxHook.bboxRendererRef.value.updateDrawing(relative)
      }
      return
    }
    
    if (!modeHook.isLeaderEditMode.value) return
    setEndpointPos(relative)
  }

  // 鼠标按下事件处理
  const handleMouseDown = ({ event, containerCoords: absolute, wrapperCoords: relative }) => {
    if (modeHook.isPreviewMode.value && props.enablePan) {
      isMouseDown.value = true
      lastMousePos.value = absolute
      return
    }
    
    if (modeHook.isBboxEditMode.value && props.enableBboxEdit) {
      // bbox编辑模式下开始绘制新的bbox
      if (bboxHook.bboxRendererRef.value && bboxHook.selectedBboxIndex.value === null) {
        bboxHook.bboxRendererRef.value.startDrawing(relative, event.target)
      }
      return
    }
    
    if (!modeHook.isLeaderEditMode.value || bboxHook.selectedBboxIndex.value === null) return
    event.preventDefault()
    isMouseDown.value = true
    setEndpointPos(relative)
  }

  // 鼠标释放事件处理
  const handleMouseUp = ({ event, wrapperCoords: relative }) => {
    if (modeHook.isPreviewMode.value) {
      isMouseDown.value = false
      lastMousePos.value = null
      return
    }
    
    if (modeHook.isBboxEditMode.value && props.enableBboxEdit) {
      // bbox编辑模式下完成绘制
      if (bboxHook.bboxRendererRef.value) {
        bboxHook.bboxRendererRef.value.finishDrawing()
      }
      return
    }
    
    if (!modeHook.isLeaderEditMode.value || bboxHook.selectedBboxIndex.value === null) return
    event.preventDefault()
    setEndpointPos(relative)

    if (isMouseDown.value) {
      isMouseDown.value = false
      nextTick(() => leaderHook.addLeaderAtPosition(relative))
    }
  }

  // 鼠标离开事件处理
  const handleMouseLeave = () => {
    isMouseDown.value = false
    lastMousePos.value = null
  }

  // 重置鼠标状态
  const resetMouseStates = () => {
    isMouseDown.value = false
    lastMousePos.value = null
  }

  return {
    // 状态
    isMouseDown,
    lastMousePos,
    
    // 方法
    handleWheel,
    handleMouseMove,
    handleMouseDown,
    handleMouseUp,
    handleMouseLeave,
    resetMouseStates,
  }
}
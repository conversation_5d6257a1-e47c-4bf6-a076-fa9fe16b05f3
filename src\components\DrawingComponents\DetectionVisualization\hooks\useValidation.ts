import { computed } from 'vue'

export function useValidation(props, modeHook, bboxHook) {
  // 当前步骤提示
  const currentHint = computed(() => {
    if (!modeHook.isLeaderEditMode?.value) return ''

    if (bboxHook.selectedBboxIndex?.value === null) {
      return '步骤1: 点击选择一个边界框'
    } else {
      return '步骤2: 点击选择目标点位置'
    }
  })

  // 提示框宽度
  const hintWidth = computed(() => {
    return Math.max(currentHint.value.length * 12 + 20, 200)
  })

  return {
    // 计算属性
    currentHint,
    hintWidth
  }
}
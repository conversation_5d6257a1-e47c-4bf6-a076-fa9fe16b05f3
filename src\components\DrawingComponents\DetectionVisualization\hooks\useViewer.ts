import { ref } from 'vue'

export function useViewer(props, emit, imageViewerRef) {
  
  // 视图控制方法
  const resetView = () => {
    if (imageViewerRef.value) {
      imageViewerRef.value.resetView()
    }
  }

  // 放缩到指定点
  const zoomToPoint = (point, zoom, coordType) => {
    if (imageViewerRef.value) {
      imageViewerRef.value.zoomToPoint(point, zoom || props.maxZoom, coordType || props.coordType)
    }
  }

  // 调整缩放
  const adjustZoom = (scaleFactor) => {
    if (imageViewerRef.value) {
      imageViewerRef.value.adjustZoom(scaleFactor)
    }
  }

  // 调整平移偏移
  const adjustPanOffset = (deltaX, deltaY) => {
    if (imageViewerRef.value) {
      imageViewerRef.value.adjustPanOffset(deltaX, deltaY)
    }
  }

  // 图像事件处理
  const handleImageLoaded = (data) => {
    emit('image-loaded', data)
  }

  const handleImageError = (error) => {
    emit('image-error', error)
  }

  return {
    // 状态
    imageViewerRef,
    
    // 方法
    resetView,
    zoomToPoint,
    adjustZoom,
    adjustPanOffset,
    handleImageLoaded,
    handleImageError
  }
}
<template>
  <div class="detection-visualization-container">

    <!-- 异常检测控件 -->
    <AnomalyDetector
      v-if="showAnomalyDetector"
      class="anomaly-detector"
      :bboxList="bboxList"
      :anomalyResults="anomalyResults"
      :categoryMap="categoryMap"
      :showStats="showAnomalyStats"
      :showAnomalyList="showAnomalyList"
      @anomaly-selected="handleAnomalySelected"
      @anomaly-highlighted="handleAnomalyHighlighted"
      @type-filter-changed="handleAnomalyTypeFilterChanged"
      ref="anomalyDetectorRef"
    />

    <BaseImageViewer
      :imageSrc="imageSrc"
      :maxWidth="maxWidth"
      :maxHeight="maxHeight"
      :minWidth="minWidth"
      :minHeight="minHeight"
      :coordsType="coordsType"
      :emptyText="emptyText"
      :imageAlt="imageAlt"
      :enableZoom="enableZoom"
      :enablePan="enablePan"
      :minZoom="minZoom"
      :maxZoom="maxZoom"
      @image-loaded="handleImageLoaded"
      @image-error="handleImageError"
      @viewer-mousemove="handleMouseMove"
      @viewer-mousedown="handleMouseDown"
      @viewer-mouseup="handleMouseUp"
      @viewer-mouseleave="handleMouseLeave"
      @viewer-wheel="handleWheel"
      :style="{ cursor: svgCursor }"
      ref="imageViewerRef"
    >
      <template #default="{ transformCoordinate, transformSize, reverseTransformPoint }">
        <!-- 边界框渲染器 -->
        <BboxRenderer
          :bboxList="bboxList"
          :transformCoordinate="transformCoordinate"
          :transformSize="transformSize"
          :reverseTransformPoint="reverseTransformPoint"
          :showLabel="showBboxLabel"
          :showCorners="showCornerPoints"
          :strokeWidth="bboxStrokeWidth"
          :highlightKeys="highlightKeys"
          :highlightAnimationKeys="highlightAnimationKeys"
          :keyField="keyField"
          :isEditMode="isBboxEditMode"
          :selectedBboxIndex="selectedBboxIndex"
          :visibleCategories="visibleCategories"
          :showAllCategories="showAllCategories"
          :newBboxCategory="newBboxCategory"
          :allCategories="allCategories"
          :categoryMap="categoryMap"
          @bbox-click="handleBboxClick"
          @bbox-selected="handleBboxSelected"
          @bbox-deselected="handleBboxDeselected"
          @bbox-updated="handleBboxUpdated"
          @bbox-deleted="handleBboxDeleted"
          @bbox-temp-updated="handleBboxTempUpdated"
          @bbox-temp-cancel="handleBboxTempCancel"
          ref="bboxRendererRef"
        />
        
        <!-- 引线渲染 -->
        <g class="leader-lines-group">
          <LeaderLine
            v-for="(leader, index) in processedLeaders"
            :key="`leader-${index}`"
            :startPoint="leader.startPoint"
            :endPoint="leader.endPoint"
            :originalCoordinate="leader.originalCoordinate"
            :endPointRadius="2"
            :color="leader.color"
            :coordsType="coordsType"
            :showCoordinates="showCoordinates"
            :showDeleteButton="isLeaderEditMode"
            :leaderData="{ ...leader, index }"
            @line-click="handleLeaderClick"
            @delete="handleLeaderDelete"
          />
        </g>
        
        <!-- 引线编辑器 -->
        <LeaderLineEditor
          v-if="isLeaderEditMode"
          :isActive="isLeaderEditMode"
          :previewEndRadius="1"
          :selectedBboxIndex="selectedBboxIndex"
          :currentMousePos="curEndpointPos"
          :bboxList="processedBboxes"
          :reverseTransformPoint="reverseTransformPoint"
          :coordsType="coordsType"
          :showPreviewCoordinates="showCoordinates"
          :bboxLeaderCounts="bboxLeaderCounts"
          :maxLeadersPerBbox="maxLeadersPerBbox"
        />
      </template>
    </BaseImageViewer>

    
    <!-- 左上角：状态信息 -->
    <div class="corner top-left">
      <!-- 缩放 -->
      <div v-if="showZoomInfo && imageViewerRef" class="zoom-box">
        缩放: {{ Math.round((imageViewerRef.zoomLevel || 1) * 100) }}%
      </div>

      <!-- 模式提示 -->
      <div class="mode-status" v-if="isLeaderEditMode">
        <div>✨ 引线编辑模式</div>
        <div class="hint-bg">
          <div class="hint-text">{{ currentHint }}</div>
          <div 
            v-if="selectedBboxIndex !== null && maxLeadersPerBbox > 0"
            class="hint-subtext"
          >
            该框引线: {{ bboxLeaderCounts[selectedBboxIndex] || 0 }}/{{ maxLeadersPerBbox }}
          </div>
          <div 
            v-if="!canAddMoreLeaders"
            class="hint-warning"
          >
            已达到引线数量限制
          </div>
        </div>
      </div>
      <div class="mode-status" v-if="isBboxEditMode">
        <div>✏️ 框体编辑模式</div>
        <div v-if="canDrawing">
          <div>🖱️ 拖拽绘制新框体</div>
          <div>🖌️ 当前类别: {{ categoryMap[newBboxCategory] || newBboxCategory }}</div>
        </div>
        <div v-else>
          <div v-if="selectedBboxIndex !== null">⚠️ 请取消选择框体后才能绘制新框体</div>
          ⚠️ 请先在类别过滤中选择且仅选择一个类别，才能绘制新框体
        </div>
      </div>
      <div class="mode-status" v-if="isPreviewMode">
        👁️ 预览模式
      </div>
    </div>

    <!-- 右上角：控制面板 -->
    <div class="corner top-right controls">
      <!-- 控制按钮 -->
      <div class="controls-card">
        <div class="mode-buttons">
          <!-- 模式切换 -->
          <button :class="['mode-btn',{active:isPreviewMode}]" @click="setPreviewMode">预览</button>
          <button :class="['mode-btn',{active:isLeaderEditMode}]" @click="setLeaderEditMode">引线</button>
          <button v-if="enableBboxEdit" :class="['mode-btn',{active:isBboxEditMode}]" @click="setBboxEditMode">框体</button>
        </div>
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button class="view-btn" @click="resetView">重置</button>
          <button v-if="isLeaderEditMode && leaders.length>0" class="clear-btn" @click="clearAllLeaders">清除引线</button>
          <button v-if="isLeaderEditMode && selectedBboxIndex!==null" class="cancel-btn" @click="cancelSelection">取消</button>
          <slot 
            name="leader-controls"
            :isLeaderEditMode="isLeaderEditMode"
            :isBboxEditMode="isBboxEditMode"
            :isPreviewMode="isPreviewMode"
            :changeStats="changeStats"
            :saveBboxChanges="saveBboxChanges"
            :cancelBboxChanges="cancelBboxChanges"
          ></slot>
        </div>
      </div>


      <!-- 类别过滤 -->
      <div v-if="bboxRendererRef?.allCategories?.length > 0" class="filter-card">
        <div class="filter-header">类别过滤</div>
        <div class="filter-buttons">
          <button @click="showAllBboxCategories" :class="['filter-btn',{active:showAllCategories}]">全部</button>
          <button v-for="c in bboxRendererRef.allCategories" :key="c"
            @click="toggleCategory(c)"
            :class="['filter-btn',{active:!showAllCategories && visibleCategories.includes(c)}]">
            {{ categoryMap[c] || c }}
          </button>
        </div>
      </div>
    </div>

    <!-- 左下角：操作提示 -->
    <div class="corner bottom-left hint">
      <div v-if="isPreviewMode">
        🖱️ 拖拽: 平移视图<br>
        🎛️ 滚轮: 缩放视图
      </div>
      <div v-if="isLeaderEditMode">
        ① 选择边界框<br>
        ② 点击图像添加引线
      </div>
      <div v-if="isBboxEditMode">
        • 拖拽绘制新框体<br>
        • 点击选择/删除框体<br>
        • 拖拽控制点调整大小
      </div>
    </div>

    <!-- 右下角：数量统计 -->
    <div class="corner bottom-right stats" v-show="!isPreviewMode">
      <div v-if="isLeaderEditMode && maxTotalLeaders > 0">
        总引线: {{ leaders.length }}/{{ maxTotalLeaders }}
      </div>
      <div v-if="isBboxEditMode">
        <div>
          未保存更改: {{ changeStats.total }}
        </div>
        <span v-if="changeStats.drag">拖拽: {{ changeStats.drag }}</span>
        <span v-if="changeStats.resize">调整: {{ changeStats.resize }}</span>
        <span v-if="changeStats.add">新增: {{ changeStats.add }}</span>
        <span v-if="changeStats.delete">删除: {{ changeStats.delete }}</span>
      </div>
    </div>
  </div>
</template>


<script setup>
import { watch, onMounted } from 'vue'
import BaseImageViewer from './components/BaseImageViewer'
import BboxRenderer from './components/BboxRenderer'
import LeaderLine from './components/LeaderLine.vue'
import LeaderLineEditor from './components/LeaderLineEditor.vue'
import AnomalyDetector from './components/AnomalyDetector'

import { 
  useBbox,
  useCategory,
  useLeader,
  useMode,
  useMouseEvents,
  useValidation,
  useViewer,
  useAnomalyDetection,
} from './hooks'

const props = defineProps({
  // 图像源
  imageSrc: {
    type: String,
    default: null
  },
  // 边界框列表
  bboxList: {
    type: Array,
    default: () => []
  },
  // 引线列表
  leaders: {
    type: Array,
    default: () => []
  },
  // 坐标类型
  coordsType: {
    type: String,
    default: 'absolute'
  },
  // 尺寸配置
  maxWidth: {
    type: Number,
    default: 800
  },
  maxHeight: {
    type: Number,
    default: 600
  },
  minWidth: {
    type: Number,
    default: 300
  },
  minHeight: {
    type: Number,
    default: 200
  },
  // 显示配置
  showBboxLabel: {
    type: Boolean,
    default: true
  },
  showCornerPoints: {
    type: Boolean,
    default: false
  },
  showCoordinates: {
    type: Boolean,
    default: true
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showZoomInfo: {
    type: Boolean,
    default: true
  },
  // 样式配置
  bboxStrokeWidth: {
    type: Number,
    default: 2
  },
  // 边界框配置
  enableBboxEdit: {
    type: Boolean,
    default: false
  },
  visibleCategories: {
    type: Array,
    default: () => []
  },
  showAllCategories: {
    type: Boolean,
    default: true
  },
  allCategories: {
    type: Array,
    default: () => []
  },
  categoryMap: {
    type: Object,
    default: () => ({})
  },
  // 默认模式
  defaultMode: {
    type: String,
    default: 'preview'
  },
  // 高亮配置
  highlightKeys: {
    type: Array,
    default: () => []
  },
  keyField: {
    type: String,
    default: 'id'
  },
  highlightAnimationKeys: {
    type: Array,
    default: () => []
  },
  // 文本配置
  emptyText: {
    type: String,
    default: '暂无图像'
  },
  imageAlt: {
    type: String,  
    default: '坐标引线图像'
  },
  // 引线数量限制
  maxTotalLeaders: {
    type: Number,
    default: 0 // 0表示不限制
  },
  maxLeadersPerBbox: {
    type: Number,
    default: 0 // 0表示不限制
  },
  // 缩放配置
  enableZoom: {
    type: Boolean,
    default: true
  },
  enablePan: {
    type: Boolean,
    default: true
  },
  zoomStep: {
    type: Number,
    default: 0.1
  },
  minZoom: {
    type: Number,
    default: 0.1
  },
  maxZoom: {
    type: Number,
    default: 5
  },
  // 新增异常检测相关props
  showAnomalyDetector: {
    type: Boolean,
    default: true
  },
  showAnomalyStats: {
    type: Boolean,
    default: true
  },
  showAnomalyList: {
    type: Boolean,
    default: true
  },
  anomalyResults: {
    type: Array,
    default: () => []
  },
})

const emits = defineEmits([
  'bbox-click',
  'bbox-selected',
  'bbox-updated',
  'bbox-deleted', 
  'bbox-temp-updated',
  'bbox-temp-cancel',
  'category-changed',
  'leader-click', 
  'leader-added',
  'leader-removed',
  'mode-changed',
  'image-loaded',
  'image-error',
  'zoom-changed',
  'pan-changed',
  'mouse-move',
  // 异常检测相关事件
  'anomaly-selected',
  'anomaly-highlighted',
  'anomaly-filter-changed',
])

const imageViewerRef = ref(null)
const bboxRendererRef = ref(null)

// 异常检测相关状态
const anomalyDetectorRef = ref(null)
const highlightedAnomalyIds = ref([])
const selectedAnomalyId = ref(null)


// 控件模式管理
const modeHook = useMode(props, emits), {
  isPreviewMode,
  isLeaderEditMode,
  isBboxEditMode,
  currentMode,
  setPreviewMode,
  setLeaderEditMode,
  setBboxEditMode,
  initializeMode
} = modeHook

// 边界框管理
const bboxHook = useBbox(props, emits, bboxRendererRef), {
  selectedBboxIndex,
  processedBboxes,
  changeStats,
  canDrawing,
  newBboxCategory,
  handleBboxClick: handleBboxClick_,
  handleBboxSelected,
  handleBboxDeselected,
  handleBboxUpdated,
  handleBboxDeleted,
  handleBboxTempUpdated,
  handleBboxTempCancel,
  resetBboxSelection,
  cancelSelection,
  saveBboxChanges,
  cancelBboxChanges
} = bboxHook

// 视图控制
const viewerHook = useViewer(props, emits, imageViewerRef), {
  resetView,
  zoomToPoint,
  adjustZoom,
  adjustPanOffset,
  handleImageLoaded,
  handleImageError
} = viewerHook


// 引线管理
const leaderHook = useLeader(props, emits, bboxHook, viewerHook), {
  curEndpointPos,
  bboxLeaderCounts,
  canAddMoreLeaders,
  processedLeaders,
  canBboxAddLeader,
  findNearestCornerInBbox,
  addLeaderAtPosition,
  handleLeaderClick,
  handleLeaderDelete,
  clearAllLeaders,
  buildLeaderData,
  resetLeaderStates
} = leaderHook

// 鼠标事件处理
const mouseHook = useMouseEvents(props, emits, modeHook, bboxHook, leaderHook, viewerHook), {
  isMouseDown,
  lastMousePos,
  handleWheel,
  handleMouseMove,
  handleMouseDown,
  handleMouseUp,
  handleMouseLeave,
  resetMouseStates,
} = mouseHook

// bbox 类别管理
const categoryHook = useCategory(props, emits), {
  toggleCategory,
  showOnlyCategory,
  showAllBboxCategories
} = categoryHook

// 数据验证
const validationHook = useValidation(props, modeHook, bboxHook), {
  currentHint,
  hintWidth
} = validationHook

const anomalyHook = useAnomalyDetection(props, emits, bboxHook), {
  handleAnomalySelected,
  handleAnomalyHighlighted,
  handleAnomalyTypeFilterChanged
} = anomalyHook

// 注入外部重置方法
modeHook.setExternalResets([
  resetBboxSelection,
  resetLeaderStates,
  resetMouseStates
])

// 计算SVG光标
const svgCursor = computed(() => {
  if (isPreviewMode.value) {
    return isMouseDown.value ? 'grabbing' : 'grab'
  }
  if (isLeaderEditMode.value) {
    return selectedBboxIndex.value === null ? 'pointer' : 'crosshair'
  }
  return 'default'
})

// 边界框点击事件处理
const handleBboxClick = (data) => {
  // 如果是引线编辑模式且尚未选择bbox，则选择这个bbox
  if (isLeaderEditMode.value && selectedBboxIndex.value === null) {
    // 检查该bbox是否还能添加引线
    if (canAddMoreLeaders.value && canBboxAddLeader(data.index)) {
      selectedBboxIndex.value = data.index
      emits('bbox-selected', {
        bbox: data.bbox,
        index: data.index
      })
    }
  } else {
    // 否则触发正常的bbox点击事件
    handleBboxClick_(data)
  }
}

// 获取元素信息
const getElemById = (id, type) => {
  if (!id || !type) return null
  if (type === 'bbox') {
    return processedBboxes.value.find(bbox => bbox.originalBbox.id === id)
  } else if (type === 'leader') {
    return processedLeaders.value.find(leader => leader.originalLeader.id === id)
  } else {
    console.warn(`Unknown type: ${type}`)
    return null
  }
}

// 监听模式变化
watch(() => props.defaultMode, (newMode) => {
  initializeMode()
})

// 监听编辑状态重置事件
watch(() => currentMode, () => {
  resetBboxSelection()
  resetLeaderStates()
  resetMouseStates()
})

// 组件挂载时初始化
onMounted(() => {
  initializeMode()
})

// 暴露方法和状态
defineExpose({
  // 模式控制
  setPreviewMode,
  setLeaderEditMode,
  setBboxEditMode,

  // 操作方法
  clearAllLeaders,
  cancelSelection,
  resetView,
  resetBboxSelection,

  // 状态
  changeStats,
  selectedBboxIndex,
  canAddMoreLeaders,
  bboxLeaderCounts,
  
  // 类别控制
  toggleCategory,
  showOnlyCategory,
  showAllBboxCategories,
  
  // 视图控制
  zoomToPoint,
  getElemById,
  buildLeaderData,
})
</script>

<style scoped lang="scss">
@use "sass:color";
// 🎨 基础变量
$color-dark: rgba(0,0,0,0.75);
$color-primary: #007bff;
$color-success: #28a745;
$color-warning: #ffc107;
$color-danger: #dc3545;
$color-info: #17a2b8;
$color-secondary: #6c757d;

$radius: 6px;
$shadow: 0 2px 6px rgba(0,0,0,0.25);
$transition: all 0.2s ease;

// 🌐 容器
.detection-visualization-container {
  position: relative;
  width: 100%;
  height: 100%;
}

// 📌 四角信息块
.corner {
  position: absolute;
  padding: 6px 10px;
  border-radius: $radius;
  background: $color-dark;
  color: white;
  font-size: 12px;
  box-shadow: $shadow;
  max-width: 240px;

  &.top-left   { top: 10px; left: 10px; }
  &.top-right  { top: 10px; right: 10px; }
  &.bottom-left  { bottom: 10px; left: 10px; }
  &.bottom-right { bottom: 10px; right: 10px; }

  &.hint  { font-size: 11px; line-height: 1.5; }
  &.stats { font-size: 11px; font-weight: bold; }
  &.controls { display: flex; flex-direction: column; gap: 8px; }
}

// 🔘 通用按钮
%btn {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: $transition;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.3);
  }

  &:disabled {
    background: $color-secondary;
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// 🔘 模式按钮
.mode-btn {
  @extend %btn;
  background: $color-success;
  color: white;

  &.active {
    background: $color-warning;
    color: #212529;
  }
}

// 🔘 操作按钮
.view-btn {
  @extend %btn;
  background: $color-info;
  color: white;

  &:hover { background: color.adjust($color-info, $lightness: -10%); }
}

.clear-btn {
  @extend %btn;
  background: $color-danger;
  color: white;

  &:hover { background: color.adjust($color-danger, $lightness: -10%); }
}

.cancel-btn {
  @extend %btn;
  background: $color-secondary;
  color: white;

  &:hover { background: color.adjust($color-secondary, $lightness: -10%); }
}

// 🎛️ 控制区（右上角）
// 右上角区域容器
.top-right {
  display: flex;
  flex-direction: column;
  gap: 10px; // 卡片之间留白
  align-items: flex-end; // 靠右排

  background: unset;
  box-shadow: unset;
}

// 🎛️ 控制按钮卡片
.controls-card {
  padding: 6px 10px;
  border-radius: $radius;
  display: flex;
  flex-direction: column;
  gap: 6px;

  .mode-buttons,
  .action-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
  }
}

// 🎛️ 类别过滤卡片
.filter-card {
  background: $color-dark;
  padding: 6px 10px;
  border-radius: $radius;
  box-shadow: $shadow;
  max-width: 240px;

  .filter-header {
    font-weight: bold;
    margin-bottom: 4px;
    color: $color-warning;
    font-size: 12px;
  }

  .filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .filter-btn {
      @extend %btn;
      font-size: 11px;
      padding: 2px 6px;
      background: $color-secondary;
      color: white;

      &:hover { background: color.adjust($color-secondary, $lightness: -10%); }
      &.active {
        background: $color-primary;
        &:hover { background: color.adjust($color-primary, $lightness: -10%); }
      }
    }
  }
}

// 📊 状态信息（左上角）
.zoom-box {
  font-size: 11px;
  margin-bottom: 4px;
}

.mode-status {
  font-size: 11px;
  font-weight: bold;
  color: $color-warning;

  .hint-bg {
    margin-top: 1rem;
  }

  .hint-text {
    font-size: 12px;
    font-weight: bold;
    color: white;
    pointer-events: none;
    user-select: none;
    line-height: 1.4;
  }

  .hint-subtext {
    font-size: 10px;
    font-weight: normal;
    color: #ffc107;
    pointer-events: none;
    user-select: none;
    margin-top: 2px;
  }

  .hint-warning {
    font-size: 10px;
    font-weight: bold;
    color: #ff4757;
    pointer-events: none;
    user-select: none;
    margin-top: 2px;
  }
}

// 💡 操作提示（左下角）
.hint {
  .hint-item { margin: 2px 0; }
}

// 📈 统计信息（右下角）
.stats {
  line-height: 1.4;

  span {
    display: inline-block;
    margin-right: 6px;
    font-weight: normal;
    color: $color-warning;
  }
}

// 异常框的特殊样式
.anomaly-detector {
  top: 100px;
  :deep(.bbox-rect.anomaly) {
    stroke-dasharray: 4,2;
    filter: drop-shadow(0 0 6px currentColor);
  }

  :deep(.bbox-label.anomaly) {
    font-weight: bold;
    filter: drop-shadow(0 0 3px rgba(0,0,0,0.5));
  }
}
</style>
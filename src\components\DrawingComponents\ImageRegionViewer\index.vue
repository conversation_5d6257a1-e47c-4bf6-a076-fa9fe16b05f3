<template>
  <div class="image-region-viewer" ref="containerRef">
    <canvas
      ref="canvasRef"
      :width="canvasWidth"
      :height="canvasHeight"
      class="region-canvas"
    />
    <div v-if="loading" class="loading">
      加载中...
    </div>
    <div v-if="error" class="error">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'

interface BboxParams {
  x: number
  y: number
  width: number
  height: number
}

interface MaskParams {
  // mask 可以是二维数组 (0表示透明，1表示显示)
  data: number[][]
  // 或者是 base64 编码的图片
  maskImage?: string
}

interface Props {
  imageUrl: string
  type: 'bbox' | 'mask'
  bboxParams?: BboxParams
  maskParams?: MaskParams
  maxWidth?: number
  maxHeight?: number
  minWidth?: number
  minHeight?: number
  // 坐标类型: 'absolute' | 'relative'
  coordsType?: 'absolute' | 'relative'
  // 是否显示原始bbox边框
  showOriginalBbox?: boolean
  // 原始bbox边框颜色
  bboxBorderColor?: string
  // 原始bbox边框宽度
  bboxBorderWidth?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxWidth: 800,
  maxHeight: 600,
  minWidth: 0,
  minHeight: 0,
  coordsType: 'absolute',
  showOriginalBbox: true,
  bboxBorderColor: '#ff0000',
  bboxBorderWidth: 2
})

// 验证 coordsType 属性
const validateCoordsType = (value: string): boolean => {
  return ['absolute', 'relative'].includes(value)
}

// 在运行时验证 coordsType
if (!validateCoordsType(props.coordsType)) {
  console.warn(`Invalid coordsType: ${props.coordsType}. Must be 'absolute' or 'relative'.`)
}

const containerRef = ref<HTMLElement>()
const canvasRef = ref<HTMLCanvasElement>()
const canvasWidth = ref(0)
const canvasHeight = ref(0)
const loading = ref(false)
const error = ref('')

let originalImage: HTMLImageElement | null = null
let maskImageElement: HTMLImageElement | null = null

const loadImage = (url: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    img.onload = () => resolve(img)
    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = url
  })
}

// 坐标转换函数
const transformCoordinate = (value: number, dimension: 'width' | 'height'): number => {
  if (!originalImage) return value
  
  if (props.coordsType === 'relative') {
    // 相对坐标（0-1）转换为绝对像素坐标
    const imageSize = dimension === 'width' ? originalImage.width : originalImage.height
    return Math.round(value * imageSize)
  } else {
    // 绝对坐标直接返回
    return Math.round(value)
  }
}

// 转换 bbox 参数
const getTransformedBboxParams = (): { original: BboxParams; expanded: BboxParams } | null => {
  if (!props.bboxParams || !originalImage) return null;

  let original: BboxParams = {
    x: transformCoordinate(props.bboxParams.x, 'width'),
    y: transformCoordinate(props.bboxParams.y, 'height'),
    width: transformCoordinate(props.bboxParams.width, 'width'),
    height: transformCoordinate(props.bboxParams.height, 'height')
  };

  // 验证转换后的坐标
  if (props.coordsType === 'relative') {
    // 相对坐标验证（原始值应该在 0-1 之间）
    const { x, y, width, height } = props.bboxParams;
    if (
      x < 0 || x > 1 || y < 0 || y > 1 ||
      width < 0 || width > 1 || height < 0 || height > 1 ||
      x + width > 1 || y + height > 1
    ) {
      throw new Error('Relative coordinates must be between 0 and 1, and bbox must be within image bounds');
    }
  } else {
    // 绝对坐标验证
    if (
      original.x < 0 ||
      original.y < 0 ||
      original.x + original.width > originalImage.width ||
      original.y + original.height > originalImage.height
    ) {
      throw new Error('Bbox parameters are out of image bounds');
    }
  }

  // minWidth/minHeight 逻辑 - 创建扩展后的bbox
  const minWidth = Math.max(0, props.minWidth);
  const minHeight = Math.max(0, props.minHeight);
  let { x, y, width, height } = original;

  // 以中心为基准扩展
  if (width < minWidth) {
    const centerX = x + width / 2;
    width = minWidth;
    x = Math.round(centerX - width / 2);
    // 边界处理
    if (x < 0) x = 0;
    if (x + width > originalImage.width) {
      x = originalImage.width - width;
      if (x < 0) x = 0;
      width = originalImage.width;
    }
  }
  if (height < minHeight) {
    const centerY = y + height / 2;
    height = minHeight;
    y = Math.round(centerY - height / 2);
    // 边界处理
    if (y < 0) y = 0;
    if (y + height > originalImage.height) {
      y = originalImage.height - height;
      if (y < 0) y = 0;
      height = originalImage.height;
    }
  }

  const expanded: BboxParams = { x, y, width, height };

  return { original, expanded };
}

// 绘制bbox边框
const drawBboxBorder = (ctx: CanvasRenderingContext2D, originalBbox: BboxParams, expandedBbox: BboxParams, scale: number) => {
  if (!props.showOriginalBbox) return;

  // 计算原始bbox在画布上的相对位置
  const relativeX = (originalBbox.x - expandedBbox.x) * scale;
  const relativeY = (originalBbox.y - expandedBbox.y) * scale;
  const relativeWidth = originalBbox.width * scale;
  const relativeHeight = originalBbox.height * scale;

  // 绘制边框
  ctx.strokeStyle = props.bboxBorderColor;
  ctx.lineWidth = props.bboxBorderWidth;
  ctx.setLineDash([]);
  ctx.strokeRect(relativeX, relativeY, relativeWidth, relativeHeight);
}

const renderBboxRegion = () => {
  if (!originalImage || !canvasRef.value || !props.bboxParams) return

  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  try {
    const bboxResult = getTransformedBboxParams()
    if (!bboxResult) return

    const { original, expanded } = bboxResult

    // 计算缩放比例以适应容器
    const scaleX = props.maxWidth / expanded.width
    const scaleY = props.maxHeight / expanded.height
    const scale = Math.min(scaleX, scaleY, 1)

    canvasWidth.value = Math.floor(expanded.width * scale)
    canvasHeight.value = Math.floor(expanded.height * scale)

    nextTick(() => {
      // 绘制裁剪后的图片区域（扩展后的区域）
      ctx.drawImage(
        originalImage!,
        expanded.x, expanded.y, expanded.width, expanded.height, // 源图片的裁剪区域
        0, 0, canvasWidth.value, canvasHeight.value // 目标画布区域
      )

      // 绘制原始bbox的边框
      drawBboxBorder(ctx, original, expanded, scale)
    })
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to render bbox region'
  }
}

const renderMaskRegion = async () => {
  if (!originalImage || !canvasRef.value || !props.maskParams) return

  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 设置画布尺寸
  const scaleX = props.maxWidth / originalImage.width
  const scaleY = props.maxHeight / originalImage.height
  const scale = Math.min(scaleX, scaleY, 1)

  canvasWidth.value = Math.floor(originalImage.width * scale)
  canvasHeight.value = Math.floor(originalImage.height * scale)

  await nextTick()

  // 先绘制原图
  ctx.drawImage(originalImage, 0, 0, canvasWidth.value, canvasHeight.value)

  // 应用蒙版
  if (props.maskParams.maskImage) {
    // 使用蒙版图片
    try {
      maskImageElement = await loadImage(props.maskParams.maskImage)
      
      // 创建临时画布处理蒙版
      const tempCanvas = document.createElement('canvas')
      const tempCtx = tempCanvas.getContext('2d')
      if (!tempCtx) return

      tempCanvas.width = canvasWidth.value
      tempCanvas.height = canvasHeight.value

      // 绘制原图到临时画布
      tempCtx.drawImage(originalImage, 0, 0, canvasWidth.value, canvasHeight.value)

      // 设置合成模式，只保留蒙版区域
      tempCtx.globalCompositeOperation = 'destination-in'
      tempCtx.drawImage(maskImageElement, 0, 0, canvasWidth.value, canvasHeight.value)

      // 清空主画布并绘制结果
      ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)
      ctx.drawImage(tempCanvas, 0, 0)
    } catch (err) {
      error.value = 'Failed to load mask image'
    }
  } else if (props.maskParams.data) {
    // 使用二维数组蒙版
    const maskData = props.maskParams.data
    const imageData = ctx.getImageData(0, 0, canvasWidth.value, canvasHeight.value)
    const pixels = imageData.data

    const maskHeight = maskData.length
    const maskWidth = maskData[0]?.length || 0

    // 如果是相对坐标模式，需要验证 mask 数据的范围
    if (props.coordsType === 'relative') {
      // 对于二维数组 mask，相对坐标模式下数组索引也应该被视为相对坐标
      // 这里可以根据具体需求调整实现
      console.info('Using relative coordinates with mask data array')
    }

    for (let y = 0; y < canvasHeight.value; y++) {
      for (let x = 0; x < canvasWidth.value; x++) {
        const maskY = Math.floor((y / canvasHeight.value) * maskHeight)
        const maskX = Math.floor((x / canvasWidth.value) * maskWidth)
        
        if (maskY < maskHeight && maskX < maskWidth) {
          const maskValue = maskData[maskY][maskX]
          if (maskValue === 0) {
            // 透明化像素
            const pixelIndex = (y * canvasWidth.value + x) * 4
            pixels[pixelIndex + 3] = 0 // Alpha 通道设为 0
          }
        }
      }
    }

    ctx.putImageData(imageData, 0, 0)
  }
}

const renderImage = async () => {
  if (!props.imageUrl) return

  loading.value = true
  error.value = ''

  try {
    originalImage = await loadImage(props.imageUrl)

    if (props.type === 'bbox') {
      if (!props.bboxParams) {
        error.value = 'Bbox parameters are required for bbox type'
        return
      }
      renderBboxRegion()
    } else if (props.type === 'mask') {
      if (!props.maskParams) {
        error.value = 'Mask parameters are required for mask type'
        return
      }
      await renderMaskRegion()
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load image'
  } finally {
    loading.value = false
  }
}

// 监听属性变化，重新渲染
watch(
  () => [props.imageUrl, props.type, props.bboxParams, props.maskParams, props.coordsType, props.showOriginalBbox, props.bboxBorderColor, props.bboxBorderWidth],
  () => {
    renderImage()
  },
  { deep: true }
)

onMounted(() => {
  renderImage()
})
</script>

<style scoped>
.image-region-viewer {
  display: inline-block;
  position: relative;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  width: 90%;
}

.region-canvas {
  display: block;
  width: 100%;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
}

.error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 0, 0, 0.1);
  color: #d73a49;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  max-width: 90%;
}
</style>
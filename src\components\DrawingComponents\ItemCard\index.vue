<template>
  <div class="item-card-wrapper">
    <div v-for="(row, rowIndex) in rows" :key="rowIndex" class="row">
      <div
        v-for="(item, colIndex) in row"
        :key="colIndex"
        class="item"
      >
        <span class="label">{{ labelMap[item.key] || item.key }}：</span>
        <span class="value">
          <span
            v-if="item.key === statusKey"
            :class="['status-chip', statusClassMap[project[item.key]] ?? 'unknown']"
          >
            {{ statusMap[project[item.key]] ?? '未定义' }}
          </span>
          <span v-else>{{ project[item.key] ?? '未定义' }}</span>
        </span>
        <span v-if="colIndex < row.length - 1" class="separator">/</span>
      </div>
    </div>
  </div>
</template>


<script setup>
import { computed } from 'vue'

const props = defineProps({
  project: {
    type: Object,
    required: true
  },
  labelMap: {
    type: Object,
    default: () => ({})
  },
  statusMap: {
    type: Object,
    default: () => ({ 0: '草稿', 1: '处理中', 2: '完成' })
  },
  statusClassMap: {
    type: Object,
    default: () => ({
      0: 'draft',
      1: 'processing',
      2: 'completed'
    })
  },
  statusKey: {
    type: String,
    default: 'status'
  },
  col: {
    type: Number,
    default: 3
  }
})

// 只显示 labelMap 中存在的字段名
const keys = computed(() => Object.keys(props.labelMap).filter(key => key in props.project))


// 分组每行 col 项，每项包含 label 和 value，共两列
const rows = computed(() => {
  const entries = keys.value.map(key => ({ key }))
  const grouped = []
  for (let i = 0; i < entries.length; i += props.col) {
    grouped.push(entries.slice(i, i + props.col))
  }
  return grouped
})
</script>

<style scoped>
.item-card-wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 每行间距 */
}

.row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px; /* 每个 item 之间的间距 */
}

.item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--el-text-color-primary); /* element-plus 主题字体色 */
}

.label {
  font-weight: 500;
  color: var(--el-text-color-secondary); /* 次要字体色 */
  margin-right: 4px;
}

.value {
  color: var(--el-text-color-primary);
}

.separator {
  margin: 0 20px;
  color: var(--el-text-color-disabled); /* 分隔符颜色 */
}

.status-chip {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* 状态样式 */
.status-chip.draft {
  background: var(--el-color-info-light-9);
  color: var(--el-color-info);
}

.status-chip.processing {
  background: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.status-chip.completed {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.status-chip.unknown {
  background: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}
</style>

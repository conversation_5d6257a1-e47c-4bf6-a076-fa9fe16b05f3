<template>
  <div class="nested-accordion" ref="accordionRef">
    <div
      v-for="(item, index) in data"
      :key="getItemKey(item, index)"
      class="accordion-item"
      :class="{ 'highlight': highlightedItem === getItemKey(item, index) }"
      :ref="el => setItemRef(getItemKey(item, index), el)"
    >
      <!-- 主要内容行 -->
      <div
        class="accordion-header"
        @click="toggleItem(getItemKey(item, index))"
      >
        <div class="header-content">
          <svg
            class="toggle-icon"
            :class="{ 'expanded': expandedItems.has(getItemKey(item, index)) }"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4.5 3L7.5 6L4.5 9"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <span class="item-title">{{ getItemTitle(item) }}</span>
        </div>
      </div>

      <!-- 展开内容 -->
      <div
        v-if="expandedItems.has(getItemKey(item, index))"
        class="accordion-content"
      >
        <!-- 当前项的详细信息 -->
        <div v-if="hasDetailInfo(item)" class="item-detail">
          <div class="detail-container" @click="handleDetailClick(item, index)">
            <div 
              v-if="getItemImageType(item).type !== 'none'" 
              class="detail-image image-hover-container"
              @mouseenter="setHoveredImage('detail', getItemKey(item, index))"
              @mouseleave="setHoveredImage(null, null)"
            >
              <img
                v-if="getItemImageType(item).type === 'img'"
                :src="getItemImageType(item).value"
                :alt="getItemTitle(item)"
              />
              <ImageRegionViewer
                v-else-if="getItemImageType(item).type === 'region'"
                :image-url="getItemImageType(item).value.imagePath"
                type="bbox"
                :min-width="props.minWidth"
                :min-height="props.minHeight"
                :bbox-params="getItemImageType(item).value.boundingBox"
              />
              <!-- 悬停工具插槽 -->
              <div 
                v-if="hoveredImageType === 'detail' && hoveredImageKey === getItemKey(item, index)"
                class="image-tools-overlay"
              >
                <slot 
                  name="image-tools" 
                  :item="item" 
                  :index="index" 
                  :mode="'detail'"
                  :image-type="getItemImageType(item).type"
                />
              </div>
            </div>
            <div class="detail-content">
              <div v-if="getItemDescription(item)" class="detail-description">
                {{ getItemDescription(item) }}
              </div>
              <div
                  v-for="(value, key) in getOtherInfo(item)"
                  :key="key"
                  class="detail-field"
                >
                  <span class="field-label">{{ formatFieldLabel(key) }}:</span>
                  <span class="field-value">{{ formatValueLabel(value) }}</span>
                </div>
            </div>
          </div>
        </div>

        <!-- 子项展示 -->
        <div v-if="hasChildren(item)" class="children-container">
          <!-- 子项数量少时，使用列表模式 -->
          <template v-if="shouldUseListMode(item)">
            <NestedAccordion
              :ref="el => setChildRef(getItemKey(item, index), el)"
              :data="getChildren(item)"
              :config="config"
              :level="props.level + 1"
              :grid-threshold="gridThreshold"
              :grid-columns="gridColumns"
              @item-click="handleChildClick"
              @item-detail-click="handleDetailClick"
              @highlight-change="handleHighlightChange"
            >
              <!-- 传递插槽到子组件 -->
              <template #image-tools="slotProps">
                <slot name="image-tools" v-bind="slotProps" />
              </template>
            </NestedAccordion>
          </template>
          
          <!-- 子项数量多时，使用网格模式 -->
          <template v-else>
            <div class="grid-container" :style="gridStyle">
              <div
                v-for="(child, childIndex) in getChildren(item)"
                :key="getItemKey(child, childIndex)"
                class="grid-item"
                :class="{ 'highlight': highlightedItem === getItemKey(child, childIndex) }"
                :ref="el => setItemRef(getItemKey(child, childIndex), el)"
                @click="handleChildClick(child, childIndex)"
              >
                <div 
                  class="grid-item-image image-hover-container"
                  @mouseenter="setHoveredImage('grid', getItemKey(child, childIndex))"
                  @mouseleave="setHoveredImage(null, null)"
                >
                  <img
                    v-if="getItemImageType(child).type === 'img'"
                    :src="getItemImageType(child).value"
                    :alt="getItemTitle(child)"
                  />
                  <ImageRegionViewer
                    v-else-if="getItemImageType(child).type === 'region'"
                    :image-url="getItemImageType(child).value.imagePath"
                    type="bbox"
                    :min-width="props.minWidth"
                    :min-height="props.minHeight"
                    :bbox-params="getItemImageType(child).value.boundingBox"
                  />
                  <div v-else class="grid-item-placeholder">
                    <span>{{ getItemTitle(child).charAt(0) }}</span>
                  </div>
                  <!-- 悬停工具插槽 -->
                  <div 
                    v-if="hoveredImageType === 'grid' && hoveredImageKey === getItemKey(child, childIndex)"
                    class="image-tools-overlay"
                  >
                    <slot 
                      name="image-tools" 
                      :item="child" 
                      :index="childIndex" 
                      :mode="'grid'"
                      :image-type="getItemImageType(child).type"
                    />
                  </div>
                </div>
                <div class="grid-item-content">
                  <div class="grid-item-title">{{ getItemTitle(child) }}</div>
                  <div v-if="getGridDescription(child)" class="grid-item-description">
                    {{ getGridDescription(child) }}
                  </div>
                  <div
                    v-for="(value, key) in getOtherInfo(child)"
                    :key="key"
                    class="detail-field"
                  >
                    <span class="field-label">{{ formatFieldLabel(key) }}:</span>
                    <span class="field-value">{{ formatValueLabel(value) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NestedAccordion from './index.vue'
import ImageRegionViewer from '../ImageRegionViewer/index.vue'
import { ref, computed, nextTick, onMounted, watch, useSlots, Comment } from 'vue'

// 组件接口定义
interface AccordionConfig {
  titleField?: string
  imageField?: string
  descriptionField?: string
  childrenField?: string
  keyField?: string
  gridDescriptionField?: string
  excludeFields?: string[]
}

interface AccordionItem {
  expandType?: 'list' | 'grid'
  expanded?: boolean // 用于初始展开状态
  [key: string]: any
}

// Props 定义
interface Props {
  data: AccordionItem[]
  config?: AccordionConfig
  level?: number
  gridThreshold?: number
  gridColumns?: number
  minWidth?: number
  minHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
  level: 0,
  gridThreshold: 4,
  gridColumns: 4,
  minWidth: 100,
  minHeight: 100
})

// 事件定义
const emit = defineEmits<{
  'item-click': [item: AccordionItem, index: number]
  'item-detail-click': [item: AccordionItem, index: number]
  'highlight-change': [itemKey: string | null]
}>()

// 默认配置
const defaultConfig: AccordionConfig = {
  titleField: 'title',
  imageField: 'image',
  descriptionField: 'description',
  childrenField: 'children',
  keyField: 'id',
  gridDescriptionField: 'description',
  excludeFields: ['children']
}

const slots = useSlots()

// 模板引用
const accordionRef = ref<HTMLElement>()
const itemRefs = ref<Map<string, HTMLElement>>(new Map())
const childRefs = ref<Map<string, any>>(new Map())

// 合并配置
const config = computed(() => ({ ...defaultConfig, ...props.config }))

// 展开状态管理
const expandedItems = ref<Set<string>>(new Set())

// 悬停状态管理
const hoveredImageType = ref<'detail' | 'grid' | null>(null)
const hoveredImageKey = ref<string | null>(null)

// 设置悬停图片状态
const setHoveredImage = (type: 'detail' | 'grid' | null, key: string | null) => {
  hoveredImageType.value = type
  hoveredImageKey.value = key
}

// 递归收集所有 expanded: true 的项的 key
const collectExpandedKeys = (data: AccordionItem[], parentIndices: number[] = []): string[] => {
  let keys: string[] = []
  data.forEach((item, index) => {
    const indices = [...parentIndices, index]
    if (item.expanded) {
      keys.push(getItemKey(item, index))
    }
    const childrenField = config.value.childrenField!
    if (Array.isArray(item[childrenField]) && item[childrenField].length > 0) {
      keys = keys.concat(collectExpandedKeys(item[childrenField], indices))
    }
  })
  return keys
}

watch(() => props.data, async (newData) => {
  expandedItems.value.clear()
  // 收集所有 expanded: true 的项的 key
  const expandedKeys = collectExpandedKeys(newData)
  // 对每个 key，找到路径并展开所有父项
  for (const key of expandedKeys) {
    const path = findItemPath(key)
    if (path) {
      for (let i = 0; i < path.length; i++) {
        expandedItems.value.add(path[i])
      }
    }
  }
}, { immediate: true, deep: true })

// 高亮状态管理
const highlightedItem = ref<string | null>(null)
const highlightTimer = ref<ReturnType<typeof setTimeout> | null>(null)

// 网格样式
const gridStyle = computed(() => ({
  gridTemplateColumns: `repeat(${props.gridColumns}, 1fr)`
}))

// 设置元素引用
const setItemRef = (key: string, el: any) => {
  if (el) {
    itemRefs.value.set(key, el)
  } else {
    itemRefs.value.delete(key)
  }
}

// 设置子组件引用
const setChildRef = (key: string, el: any) => {
  if (el) {
    childRefs.value.set(key, el)
  } else {
    childRefs.value.delete(key)
  }
}

// 切换展开/折叠状态
const toggleItem = (key: string) => {
  if (expandedItems.value.has(key)) {
    expandedItems.value.delete(key)
  } else {
    expandedItems.value.add(key)
  }
}

// 展开指定项
const expandItem = (key: string) => {
  expandedItems.value.add(key)
}

// 查找项目路径
const findItemPath = (targetKey: string, currentData: AccordionItem[] = props.data, path: string[] = []): string[] | null => {
  for (let i = 0; i < currentData.length; i++) {
    const item = currentData[i]
    const itemKey = getItemKey(item, i)
    const currentPath = [...path, itemKey]
    
    // 如果找到目标项
    if (itemKey === targetKey) {
      return currentPath
    }
    
    // 在子项中递归查找
    if (hasChildren(item)) {
      const children = getChildren(item)
      const childPath = findItemPath(targetKey, children, currentPath)
      if (childPath) {
        return childPath
      }
    }
  }
  
  return null
}

// 跳转到指定项目
const navigateToItem = async (targetKey: string) => {
  // 清除之前的高亮
  clearHighlight()
  
  // 查找目标项的路径
  const path = findItemPath(targetKey)
  if (!path) {
    console.warn(`未找到指定的项目: ${targetKey}`)
    return false
  }
  
  // 展开路径上的所有父项（除了最后一个目标项）
  for (let i = 0; i < path.length - 1; i++) {
    expandItem(path[i])
  }
  
  // 等待DOM更新
  await nextTick()
  
  // 尝试在当前组件中查找元素
  let targetElement = itemRefs.value.get(targetKey)
  
  // 如果在当前组件中没找到，尝试在子组件中查找
  if (!targetElement) {
    for (const [key, childComponent] of childRefs.value) {
      if (childComponent?.navigateToItem) {
        const found = await childComponent.navigateToItem(targetKey)
        if (found) {
          return true
        }
      }
    }
  }
  
  // 如果找到了元素，进行滚动和高亮
  if (targetElement) {
    // 滚动到目标元素
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest'
    })
    
    // 等待滚动完成后开始高亮
    setTimeout(() => {
      startHighlight(targetKey)
    }, 500)
    
    return true
  }
  
  return false
}

// 开始高亮效果
const startHighlight = (itemKey: string) => {
  highlightedItem.value = itemKey
  emit('highlight-change', itemKey)
  
  // 清除之前的定时器
  if (highlightTimer.value) {
    clearTimeout(highlightTimer.value)
  }
  
  // 3秒后清除高亮
  highlightTimer.value = setTimeout(() => {
    clearHighlight()
  }, 3000)
}

// 清除高亮效果
const clearHighlight = () => {
  if (highlightTimer.value) {
    clearTimeout(highlightTimer.value)
    highlightTimer.value = null
  }
  highlightedItem.value = null
  emit('highlight-change', null)
}

// 处理子组件高亮变化
const handleHighlightChange = (itemKey: string | null) => {
  emit('highlight-change', itemKey)
}

// 获取项目唯一标识
const getItemKey = (item: AccordionItem, index: number): string => {
  const keyField = config.value.keyField!
  return item[keyField] || `item-${props.level}-${index}`
}

// 获取项目标题
const getItemTitle = (item: AccordionItem): string => {
  const titleField = config.value.titleField!
  return item[titleField] || '未命名项目'
}

// 获取项目图片类型和数据
const getItemImageType = (item: AccordionItem) => {
  const imageField = config.value.imageField!
  const value = item[imageField]
  if (!value) return { type: 'none', value: null }
  if (typeof value === 'string') {
    return { type: 'img', value }
  }
  if (
    typeof value === 'object' &&
    value.imagePath &&
    value.boundingBox
  ) {
    return { type: 'region', value }
  }
  return { type: 'none', value: null }
}

// 获取项目描述
const getItemDescription = (item: AccordionItem): string | null => {
  const descriptionField = config.value.descriptionField!
  return item[descriptionField] || null
}

// 获取网格模式描述
const getGridDescription = (item: AccordionItem): string | null => {
  const gridDescField = config.value.gridDescriptionField!
  return item[gridDescField] || null
}

// 检查是否有详细信息
const hasDetailInfo = (item: AccordionItem): boolean => {
  return !!(getItemImageType(item).type !== 'none' || getItemDescription(item) || Object.keys(getOtherInfo(item)).length > 0)
}

// 获取其他信息字段
const getOtherInfo = (item: AccordionItem): Record<string, any> => {
  const excludeFields = new Set([
    config.value.titleField,
    config.value.childrenField,
    config.value.imageField,
    config.value.descriptionField,
    ...(config.value.excludeFields || [])
  ])

  const result: Record<string, any> = {}
  
  Object.keys(item).forEach(key => {
    if (!excludeFields.has(key) && item[key] != null && item[key] !== '') {
      result[key] = item[key]
    }
  })

  return result
}

// 格式化字段标签
const formatFieldLabel = (key: string): string => {
  return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
}

// 格式化字段值
const formatValueLabel = (value: any): string => {
  if (typeof value === 'number') {
    return value.toFixed(2)
  }
  return String(value)
}

// 检查是否有子项
const hasChildren = (item: AccordionItem): boolean => {
  const childrenField = config.value.childrenField!
  return Array.isArray(item[childrenField]) && item[childrenField].length > 0
}

// 获取子项数据
const getChildren = (item: AccordionItem): AccordionItem[] => {
  const childrenField = config.value.childrenField!
  return item[childrenField] || []
}

// 判断是否使用列表模式
const shouldUseListMode = (item: AccordionItem): boolean => {
  if (item.expandType) {
    if (item.expandType === 'list') {
      return true
    } else if (item.expandType === 'grid') {
      return false
    }
  }
  const children = getChildren(item)
  return children.length <= props.gridThreshold
}

// 处理子项点击
const handleChildClick = (child: AccordionItem, index: number) => {
  emit('item-click', child, index)
}

// 处理当前项点击
const handleDetailClick = (item: AccordionItem, index: number) => {
  emit('item-detail-click', item, index)
}

// 暴露方法给父组件
defineExpose({
  navigateToItem,
  clearHighlight
})
</script>

<style scoped>
.nested-accordion {
  width: 100%;
}

.accordion-item {
  border: 1px solid #e5e7eb;
  background: #ffffff;
  transition: all 0.3s ease;
}

.accordion-item + .accordion-item {
  border-top: none;
}

/* 高亮效果 */
.accordion-item.highlight {
  background: linear-gradient(90deg, #fef3c7, #ffffff, #fef3c7);
  background-size: 200% 100%;
  animation: highlight-flash 3s ease-in-out;
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
}

@keyframes highlight-flash {
  0%, 100% {
    background-position: 0% 50%;
  }
  25%, 75% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 0% 50%;
  }
}

.accordion-header {
  width: 100%;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.15s ease;
  border: none;
  background: none;
}

.accordion-header:hover {
  background-color: #f9fafb;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-icon {
  width: 12px;
  height: 12px;
  color: #6b7280;
  transition: transform 0.15s ease;
  flex-shrink: 0;
}

.toggle-icon.expanded {
  transform: rotate(90deg);
}

.item-title {
  font-weight: 500;
  color: #111827;
  font-size: 14px;
}

.accordion-content {
  border-top: 1px solid #f3f4f6;
  background-color: #fafbfc;
}

.item-detail {
  padding: 16px;
}

.detail-container {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  cursor: pointer;
}

/* 图片悬停容器 */
.image-hover-container {
  position: relative;
  overflow: visible;
}

.detail-image {
  flex-shrink: 0;
  width: 120px;
  height: 80px;
  overflow: hidden;
  background-color: #f3f4f6;
  display: flex;
  justify-content: center;
  align-items: center;
}

.detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 图片工具覆盖层 */
.image-tools-overlay {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 10;
  opacity: 0;
  transform: translateY(-4px);
  animation: slideInFade 0.2s ease-out forwards;
}

@keyframes slideInFade {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detail-content {
  flex: 1;
  min-width: 0;
}

.detail-description {
  color: #374151;
  line-height: 1.5;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-field {
  display: flex;
  margin-bottom: 6px;
  font-size: 12px;
  align-items: flex-start;
  gap: 8px;
}

.field-label {
  font-weight: 500;
  color: #6b7280;
  flex-shrink: 0;
}

.field-value {
  color: #111827;
  word-break: break-word;
  flex: 1;
  min-width: 0;
  overflow-wrap: break-word;
  line-height: 1.4;
}

.children-container {
  border-top: 1px solid #e5e7eb;
}

/* 网格布局样式 */
.grid-container {
  display: grid;
  gap: 16px;
  padding: 16px;
}

.grid-item {
  border: 1px solid #e5e7eb;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.grid-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 网格项高亮效果 */
.grid-item.highlight {
  background: linear-gradient(45deg, #fef3c7, #ffffff, #fef3c7);
  background-size: 200% 200%;
  animation: grid-highlight-flash 3s ease-in-out;
  border-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
}

@keyframes grid-highlight-flash {
  0%, 100% {
    background-position: 0% 50%;
  }
  33% {
    background-position: 100% 0%;
  }
  66% {
    background-position: 0% 100%;
  }
}

.grid-item-image {
  width: 100%;
  height: 145px;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.grid-item-placeholder {
  width: 60px;
  height: 60px;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 24px;
  color: #6b7280;
}

.grid-item-content {
  padding: 12px;
}

.grid-item-title {
  font-weight: 500;
  color: #111827;
  font-size: 14px;
  margin-bottom: 4px;
}

.grid-item-description {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.4;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 嵌套层级样式 */
.nested-accordion .nested-accordion .accordion-item {
  margin-left: 20px;
}

.nested-accordion .nested-accordion .item-title {
  font-size: 13px;
}

.nested-accordion .nested-accordion .item-detail {
  padding: 12px;
}

.nested-accordion .nested-accordion .detail-image {
  width: 80px;
  height: 60px;
}
</style>
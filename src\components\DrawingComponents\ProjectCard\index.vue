<template>
  <div class="project-table-wrapper">
    <table class="project-table">
      <tbody>
        <tr v-for="(row, rowIndex) in rows" :key="rowIndex">
          <template v-for="(item, colIndex) in row" :key="colIndex">
            <td class="label">{{ labelMap[item.key] || item.key }}：</td>
            <td class="value">
              <span
                v-if="item.key === statusKey"
                :class="['status-chip', statusClassMap[project[item.key]] ?? 'unknown']"
              >
                {{ statusMap[project[item.key]] ?? '未知' }}
              </span>
              <span v-else>{{ project[item.key] }}</span>
            </td>
          </template>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  project: {
    type: Object,
    required: true
  },
  labelMap: {
    type: Object,
    default: () => ({})
  },
  statusMap: {
    type: Object,
    default: () => ({ 0: '草稿', 1: '处理中', 2: '完成' })
  },
  statusClassMap: {
    type: Object,
    default: () => ({
      0: 'draft',
      1: 'processing',
      2: 'completed'
    })
  },
  statusKey: {
    type: String,
    default: 'status'
  },
  col: {
    type: Number,
    default: 3
  }
})

// 只显示 labelMap 中存在的字段名
const keys = computed(() => Object.keys(props.labelMap).filter(key => key in props.project))


// 分组每行 col 项，每项包含 label 和 value，共两列
const rows = computed(() => {
  const entries = keys.value.map(key => ({ key }))
  const grouped = []
  for (let i = 0; i < entries.length; i += props.col) {
    grouped.push(entries.slice(i, i + props.col))
  }
  return grouped
})
</script>

<style scoped>
.project-table-wrapper {
  padding: 8px;
  background-color: #fafafa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow-x: auto;
  margin-bottom: 10px;
}

.project-table {
  width: 100%;
  font-size: 13px;
  color: #444;
  border-collapse: collapse;
}

.project-table td {
  padding: 6px 8px;
}

.label {
  font-weight: bold;
  color: #666;
  white-space: nowrap;
  padding-right: 6px;
  width: 80px;
}

.value {
  white-space: nowrap;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #f5f5f5;
}

.status-chip {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.draft {
  background-color: #f5f5f5;
  color: #999;
}

.processing {
  background-color: #fff7e6;
  color: #d48806;
}

.completed {
  background-color: #e6fffb;
  color: #08979c;
}

.unknown {
  background-color: #fff1f0;
  color: #cf1322;
}
</style>

<template>
  <div class="text-editor-container" ref="containerRef">
    <!-- 画布容器 -->
    <div 
      class="canvas-wrapper"
      ref="canvasWrapperRef"
      :style="{
        width: '100%',
        height: containerHeight + 'px',
        overflow: 'hidden',
        position: 'relative',
        border: '1px solid #ccc',
        cursor: isDragging ? 'grabbing' : 'grab'
      }"
      @wheel="handleWheel"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp"
    >
      <div 
        class="canvas-container" 
        :style="{ 
          width: actualCanvasWidth + 'px', 
          height: actualCanvasHeight + 'px',
          position: 'absolute',
          transform: `translate(${panX}px, ${panY}px) scale(${scale})`,
          transformOrigin: '0 0',
          transition: isAnimating ? 'transform 0.3s ease' : 'none',
          backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
          backgroundSize: backgroundSize,
          backgroundPosition: backgroundPosition,
          backgroundRepeat: backgroundRepeat,
          backgroundColor: backgroundImage ? 'transparent' : 'white'
        }"
      >
        <!-- 背景图片蒙版层（可选） -->
        <div 
          v-if="backgroundImage && backgroundOpacity < 1"
          class="background-overlay"
          :style="{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'white',
            opacity: 1 - backgroundOpacity,
            pointerEvents: 'none',
            zIndex: 1
          }"
        />
        
        <!-- 渲染每个文本框 -->
        <div
          v-for="(item, index) in textItems"
          :key="item.id"
          class="text-item"
          :style="{
            position: 'absolute',
            left: (item.bbox[0] - canvasBounds.minX) + 'px',
            top: (item.bbox[1] - canvasBounds.minY) + 'px',
            width: (item.bbox[2] - item.bbox[0]) + 'px',
            height: (item.bbox[3] - item.bbox[1]) + 'px',
            border: editingIndex === index ? '2px solid #007bff' : '1px solid #ddd',
            backgroundColor: editingIndex === index ? '#f8f9fa' : textBoxBackgroundColor,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            padding: '4px',
            boxSizing: 'border-box',
            pointerEvents: isDragging ? 'none' : 'auto',
            zIndex: 10
          }"
          @click="startEdit(index)"
          @mousedown.stop
        >
          <!-- 编辑状态 - 使用textarea支持换行 -->
          <textarea
            v-if="editingIndex === index"
            v-model="editingText"
            @blur="finishEdit"
            @keydown="handleKeydown"
            ref="editTextarea"
            class="edit-textarea"
            :style="{
              width: '100%',
              height: '100%',
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              fontSize: '0.8rem',
              padding: '0',
              resize: 'none',
              fontFamily: 'inherit',
              lineHeight: '1.4'
            }"
          />
          <!-- 显示状态 -->
          <div
            v-else
            class="text-content"
            :style="{
              fontSize: '0.8rem',
              wordBreak: 'break-word',
              overflow: 'hidden',
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'flex-start',
              lineHeight: '1.4',
              whiteSpace: 'pre-wrap'
            }"
          >
            {{ item.text }}
          </div>
          
          <!-- 显示ID标识（可选，用于调试） -->
          <div 
            v-if="showIds"
            class="id-label"
            :style="{
              position: 'absolute',
              top: '-2px',
              right: '-2px',
              backgroundColor: '#007bff',
              color: 'white',
              fontSize: Math.max(8, 10 * scale) + 'px',
              padding: '2px 4px',
              borderRadius: '2px',
              pointerEvents: 'none',
              zIndex: 20
            }"
          >
            {{ item.id }}
          </div>
        </div>
        
        <!-- 画布边界指示器（调试用） -->
        <div 
          v-if="showCanvasBounds"
          class="canvas-bounds-indicator"
          :style="{
            position: 'absolute',
            top: '0px',
            left: '0px',
            width: '100%',
            height: '100%',
            border: '2px dashed #ff6b6b',
            pointerEvents: 'none',
            zIndex: 5
          }"
        />
      </div>
      
      <!-- 缩放信息显示 -->
      <div 
        v-if="showZoomInfo"
        class="zoom-info"
        :style="{
          position: 'absolute',
          top: '10px',
          right: '10px',
          background: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '12px',
          pointerEvents: 'none',
          zIndex: 1000
        }"
      >
        {{ Math.round(scale * 100) }}%
      </div>
      
      <!-- 画布信息显示 -->
      <div 
        v-if="showCanvasInfo"
        class="canvas-info"
        :style="{
          position: 'absolute',
          top: '10px',
          left: '10px',
          background: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '11px',
          pointerEvents: 'none',
          zIndex: 1000,
          fontFamily: 'monospace'
        }"
      >
        画布: {{ actualCanvasWidth }}×{{ actualCanvasHeight }}<br>
        模式: {{ canvasSizeMode }}<br>
        容器: {{ containerSize.width }}×{{ containerSize.height }}<br>
        范围: ({{ canvasBounds.minX }}, {{ canvasBounds.minY }}) - ({{ canvasBounds.maxX }}, {{ canvasBounds.maxY }})
      </div>
    </div>
    
    <!-- 简化的控制面板 - 只保留关键按钮 -->
    <div v-if="showControlPanel" class="control-panel" style="margin-top: 10px;">
      <div class="control-row">
        <button @click="resetTexts" class="btn btn-secondary">重置文本</button>
        <button @click="fitToContainer" class="btn btn-primary">适应窗口</button>
        <button @click="zoomIn" class="btn btn-success">放大</button>
        <button @click="zoomOut" class="btn btn-warning">缩小</button>
         <el-button
          @click="() => updateTextById()"
          :disabled="updateLoading"
          type="success"
          size="small"
          :loading="updateLoading"
        >
          {{ updateLoading ? "保存中..." : "保存文本" }}
        </el-button>
        
        <!-- 可选的调试按钮 -->
        <template v-if="enableDebugButtons">
          <button @click="toggleShowIds" class="btn btn-info">
            {{ showIds ? '隐藏ID' : '显示ID' }}
          </button>
          <button @click="toggleCanvasBounds" class="btn btn-info">
            {{ showCanvasBounds ? '隐藏边界' : '显示边界' }}
          </button>
          <button @click="forceUpdateSize" class="btn btn-info">
            强制更新尺寸
          </button>
        </template>
      </div>
      
      <!-- 操作提示 -->
      <div class="control-row" style="margin-top: 10px;">
        <span style="color: #666; font-size: 14px;">
          鼠标滚轮缩放，拖动平移画布，点击文本框编辑
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import { updatePageText } from '@/api/geodrawing/page'

const updateLoading = ref(false)

// 定义文本项接口
interface TextItem {
  id: string | number
  bbox: [number, number, number, number]
  text: string
}

interface Props {
  drawingPageId: number  // 添加页面ID属性
  textItems: TextItem[]
  canvasWidth?: number
  canvasHeight?: number
  showIds?: boolean
  containerHeight?: number
  
  // 画布配置
  canvasSizeMode?: 'fixed' | 'auto' | 'hybrid'
  canvasPadding?: number
  
  // 背景图片配置
  backgroundImage?: string
  backgroundSize?: string
  backgroundPosition?: string
  backgroundRepeat?: string
  backgroundOpacity?: number
  
  // 文本框样式
  textBoxBackgroundColor?: string
  
  // 显示控制
  showControlPanel?: boolean
  showZoomInfo?: boolean
  showCanvasInfo?: boolean
  showCanvasBounds?: boolean
  enableDebugButtons?: boolean
  
  // 缩放配置
  minScale?: number
  maxScale?: number
  scaleStep?: number
  initialScale?: number
  initialPanX?: number
  initialPanY?: number
  
  // 自动适应配置
  autoFitOnResize?: boolean
  fitDebounceMs?: number

  // 自动定位配置
  autoZoomOnEdit?: boolean
  editZoomScale?: number
  editZoomPadding?: number
  editZoomAnimationDuration?: number
}

const props = withDefaults(defineProps<Props>(), {
  drawingPageId: undefined,
  canvasWidth: 800,
  canvasHeight: 600,
  showIds: false,
  containerHeight: 500,
  
  // 画布配置默认值
  canvasSizeMode: 'fixed',
  canvasPadding: 50,
  
  // 背景图片默认值
  backgroundImage: '',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
  backgroundOpacity: 1,
  
  // 文本框样式默认值
  textBoxBackgroundColor: 'rgba(255,255,255,0.9)',
  
  // 显示控制默认值
  showControlPanel: true,
  showZoomInfo: true,
  showCanvasInfo: true,
  showCanvasBounds: false,
  enableDebugButtons: true,
  
  // 缩放配置默认值
  minScale: 0.1,
  maxScale: 5,
  scaleStep: 0.1,
  initialScale: 1,
  initialPanX: 0,
  initialPanY: 0,
  
  // 自动适应配置默认值
  autoFitOnResize: true,
  fitDebounceMs: 150,

  // 自动定位配置默认值
  autoZoomOnEdit: true,
  editZoomScale: 2,
  editZoomPadding: 50,
  editZoomAnimationDuration: 500
})

// 定义emits
const emit = defineEmits<{
  "text-updated": [
    data: {
      drawingPageId: number;
      updatedTexts: any[];
      changedItems: TextItem[];
      totalChanges: number;
    }
  ]
  'update:textItems': [textItems: TextItem[]]
  'text-changed': [item: TextItem, oldText: string, newText: string]
  'item-updated': [id: string | number, newText: string, oldText: string]
  'background-changed': [backgroundImage: string, backgroundSize: string, backgroundPosition: string, backgroundOpacity: number]
  'canvas-size-changed': [width: number, height: number, bounds: any]
  'zoom-changed': [scale: number, panX: number, panY: number]
  'container-resized': [width: number, height: number]
}>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const canvasWrapperRef = ref<HTMLElement>()
const editTextarea = ref<HTMLTextAreaElement[]>()
const editingIndex = ref<number>(-1)
const editingText = ref<string>('')
const originalText = ref<string>('')
const showIds = ref(props.showIds)
const showCanvasBounds = ref(props.showCanvasBounds)

// 缩放和平移相关
const scale = ref<number>(props.initialScale)
const panX = ref<number>(props.initialPanX)
const panY = ref<number>(props.initialPanY)
const isDragging = ref<boolean>(false)
const isAnimating = ref<boolean>(false)
const lastMouseX = ref<number>(0)
const lastMouseY = ref<number>(0)

// 容器尺寸跟踪
const containerSize = reactive({
  width: 0,
  height: 0
})

// 内部文本项数据
const textItems = reactive<TextItem[]>([])

// ResizeObserver 实例
let resizeObserver: ResizeObserver | null = null
let resizeTimer: ReturnType<typeof setTimeout> | null = null

// 计算基于文本项的画布边界
const textBasedBounds = computed(() => {
  if (textItems.length === 0) {
    return { 
      minX: 0, 
      minY: 0, 
      maxX: 400, 
      maxY: 300 
    }
  }
  
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
  
  textItems.forEach(item => {
    minX = Math.min(minX, item.bbox[0])
    minY = Math.min(minY, item.bbox[1])
    maxX = Math.max(maxX, item.bbox[2])
    maxY = Math.max(maxY, item.bbox[3])
  })
  
  return {
    minX: minX - props.canvasPadding,
    minY: minY - props.canvasPadding,
    maxX: maxX + props.canvasPadding,
    maxY: maxY + props.canvasPadding
  }
})

// 计算画布实际边界（根据模式决定）
const canvasBounds = computed(() => {
  const textBounds = textBasedBounds.value
  
  switch (props.canvasSizeMode) {
    case 'fixed':
      return {
        minX: 0,
        minY: 0,
        maxX: props.canvasWidth,
        maxY: props.canvasHeight
      }
    
    case 'auto':
      return textBounds
    
    case 'hybrid':
      return {
        minX: Math.min(0, textBounds.minX),
        minY: Math.min(0, textBounds.minY),
        maxX: Math.max(props.canvasWidth, textBounds.maxX),
        maxY: Math.max(props.canvasHeight, textBounds.maxY)
      }
    
    default:
      return textBounds
  }
})

// 计算画布实际尺寸
const actualCanvasWidth = computed(() => {
  return canvasBounds.value.maxX - canvasBounds.value.minX
})

const actualCanvasHeight = computed(() => {
  return canvasBounds.value.maxY - canvasBounds.value.minY
})

// 更新容器尺寸
const updateContainerSize = () => {
  if (!canvasWrapperRef.value) return
  
  const rect = canvasWrapperRef.value.getBoundingClientRect()
  const newWidth = rect.width
  const newHeight = rect.height
  
  const hasChanged = containerSize.width !== newWidth || containerSize.height !== newHeight
  
  if (hasChanged) {
    containerSize.width = newWidth
    containerSize.height = newHeight
    
    emit('container-resized', newWidth, newHeight)
    
    // 如果启用自动适应，则延迟执行 fitToContainer
    if (props.autoFitOnResize) {
      debouncedFitToContainer()
    }
  }
}

// 防抖的 fitToContainer
const debouncedFitToContainer = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  
  resizeTimer = setTimeout(() => {
    if (!isDragging.value && editingIndex.value === -1) {
      fitToContainer()
    }
  }, props.fitDebounceMs)
}

// 强制更新尺寸（用于调试）
const forceUpdateSize = () => {
  updateContainerSize()
  nextTick(() => {
    fitToContainer()
  })
}

// 初始化 ResizeObserver
const initResizeObserver = () => {
  if (typeof ResizeObserver !== 'undefined' && canvasWrapperRef.value) {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect
        
        const hasChanged = containerSize.width !== width || containerSize.height !== height
        
        if (hasChanged) {
          containerSize.width = width
          containerSize.height = height
          emit('container-resized', width, height)
          
          if (props.autoFitOnResize) {
            debouncedFitToContainer()
          }
        }
      }
    })
    
    resizeObserver.observe(canvasWrapperRef.value)
  } else {
    console.warn('ResizeObserver 不可用，回退到手动检测')
    // 回退方案：使用定时器定期检查
    const checkSizeInterval = setInterval(() => {
      updateContainerSize()
    }, 500)
    
    // 在组件卸载时清除定时器
    onUnmounted(() => {
      clearInterval(checkSizeInterval)
    })
  }
}

// 重新计算画布
const recalculateCanvas = () => {
  nextTick(() => {
    emit('canvas-size-changed', actualCanvasWidth.value, actualCanvasHeight.value, canvasBounds.value)
  })
}

// 切换显示画布边界
const toggleCanvasBounds = () => {
  showCanvasBounds.value = !showCanvasBounds.value
}

// 切换显示ID
const toggleShowIds = () => {
  showIds.value = !showIds.value
}

// 初始化文本项
const initializeTextItems = () => {
  textItems.splice(0, textItems.length)
  props.textItems.forEach(item => {
    textItems.push({
      id: item.id,
      bbox: [...item.bbox] as [number, number, number, number],
      text: item.text
    })
  })
  
  nextTick(() => {
    recalculateCanvas()
  })
}

// 适应容器大小（改进版）
const fitToContainer = () => {
  if (!canvasWrapperRef.value) {
    console.warn('canvasWrapperRef 不可用')
    return
  }
  
  // 使用多种方式获取容器尺寸，确保准确性
  const rect = canvasWrapperRef.value.getBoundingClientRect()
  const computedStyle = window.getComputedStyle(canvasWrapperRef.value)
  const paddingLeft = parseFloat(computedStyle.paddingLeft) || 0
  const paddingRight = parseFloat(computedStyle.paddingRight) || 0
  const paddingTop = parseFloat(computedStyle.paddingTop) || 0
  const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0
  const borderLeft = parseFloat(computedStyle.borderLeftWidth) || 0
  const borderRight = parseFloat(computedStyle.borderRightWidth) || 0
  const borderTop = parseFloat(computedStyle.borderTopWidth) || 0
  const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 0
  
  const containerWidth = rect.width - paddingLeft - paddingRight - borderLeft - borderRight
  const containerHeight = rect.height - paddingTop - paddingBottom - borderTop - borderBottom
    
  if (containerWidth <= 0 || containerHeight <= 0) {
    return
  }
  
  const contentWidth = actualCanvasWidth.value
  const contentHeight = actualCanvasHeight.value
  
  if (contentWidth <= 0 || contentHeight <= 0) {
    return
  }
  
  const scaleX = containerWidth / contentWidth
  const scaleY = containerHeight / contentHeight
  const newScale = Math.min(scaleX, scaleY, 1)
    
  isAnimating.value = true
  scale.value = Math.max(props.minScale, newScale)
  panX.value = (containerWidth - contentWidth * scale.value) / 2
  panY.value = (containerHeight - contentHeight * scale.value) / 2
  
  emit('zoom-changed', scale.value, panX.value, panY.value)
  
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}

// 重置缩放
const resetZoom = () => {
  isAnimating.value = true
  scale.value = props.initialScale
  panX.value = props.initialPanX
  panY.value = props.initialPanY
  
  emit('zoom-changed', scale.value, panX.value, panY.value)
  
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}

// 放大
const zoomIn = () => {
  const newScale = Math.min(props.maxScale, scale.value + props.scaleStep)
  if (newScale !== scale.value) {
    scale.value = newScale
    emit('zoom-changed', scale.value, panX.value, panY.value)
  }
}

// 缩小
const zoomOut = () => {
  const newScale = Math.max(props.minScale, scale.value - props.scaleStep)
  if (newScale !== scale.value) {
    scale.value = newScale
    emit('zoom-changed', scale.value, panX.value, panY.value)
  }
}

// 处理鼠标滚轮事件
const handleWheel = (event: WheelEvent) => {
  if (editingIndex.value !== -1) return
  
  event.preventDefault()
  
  const rect = canvasWrapperRef.value?.getBoundingClientRect()
  if (!rect) return
  
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top
  
  const delta = event.deltaY > 0 ? -props.scaleStep : props.scaleStep
  const newScale = Math.max(props.minScale, Math.min(props.maxScale, scale.value + delta))
  
  if (newScale !== scale.value) {
    const scaleRatio = newScale / scale.value
    panX.value = mouseX - (mouseX - panX.value) * scaleRatio
    panY.value = mouseY - (mouseY - panY.value) * scaleRatio
    scale.value = newScale
    emit('zoom-changed', scale.value, panX.value, panY.value)
  }
}

// 处理鼠标按下事件
const handleMouseDown = (event: MouseEvent) => {
  if (event.button !== 0) return
  if (editingIndex.value !== -1) return
  
  isDragging.value = true
  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY
  event.preventDefault()
}

// 处理鼠标移动事件
const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - lastMouseX.value
  const deltaY = event.clientY - lastMouseY.value
  
  panX.value += deltaX
  panY.value += deltaY
  
  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY
  
  emit('zoom-changed', scale.value, panX.value, panY.value)
}

const updateTextById = async (id?: number) => {
  try {
    updateLoading.value = true;

    // 获取页面ID，优先使用传入参数，其次使用props中的值
    const targetPageId = id || props.drawingPageId;
    if (!targetPageId || typeof targetPageId !== "number") {
      console.error("无效的 drawingPageId:", targetPageId);
      return false;
    }

    // 获取所有有变化的文本项
    const changedItems = textItems.filter((item, index) => {
      const original = props.textItems[index];
      return original && item.text !== original.text;
    });

    if (changedItems.length === 0) {
      console.log("没有文本变化，无需保存");
      return true;
    }

    // 构造请求数据格式（参考后端接口要求）
    const requestList = changedItems.map((item) => ({
      item: {
        id: item.id, // OCR ID
      },
      newText: item.text, // 新的文本内容
    }));

    console.log("准备保存文本变化:", { targetPageId, requestList });

    // 调用后端API
    const response = await updatePageText(targetPageId, requestList);

    if (response.code === 200) {
      console.log("文本保存成功");
      // 触发更新事件，通知父组件
      emit("text-updated", {
        drawingPageId: targetPageId,
        updatedTexts: requestList,
        changedItems: changedItems,
        totalChanges: changedItems.length,
      });

      return true;
    } else {
      console.error("文本保存失败:", response.msg || "未知错误");
      return false;
    }
  } catch (error) {
    console.error("保存文本编辑失败:", error);
    return false;
  } finally {
    updateLoading.value = false;
  }
};

// 处理鼠标抬起事件
const handleMouseUp = () => {
  isDragging.value = false
}

// 监听props变化
watch(() => props.textItems, () => {
  initializeTextItems()
}, { immediate: true, deep: true })

// 监听缩放和平移相关props变化
watch([() => props.initialScale, () => props.initialPanX, () => props.initialPanY], () => {
  scale.value = props.initialScale
  panX.value = props.initialPanX
  panY.value = props.initialPanY
})

// 监听画布配置变化
watch([() => props.canvasSizeMode, () => props.canvasPadding, () => props.canvasWidth, () => props.canvasHeight], () => {
  recalculateCanvas()
})

// 监听容器高度变化
watch(() => props.containerHeight, () => {
  nextTick(() => {
    updateContainerSize()
    if (props.autoFitOnResize) {
      debouncedFitToContainer()
    }
  })
})

// 组件挂载后自动适应容器
onMounted(() => {
  nextTick(() => {
    // 初始化容器尺寸
    updateContainerSize()
    
    // 初始化 ResizeObserver
    initResizeObserver()
    
    // 重新计算画布
    recalculateCanvas()
    
    // 延迟执行 fitToContainer，确保所有尺寸都已稳定
    setTimeout(() => {
      fitToContainer()
    }, 200)
  })
  
  // 监听全局 resize 事件作为备用方案
  window.addEventListener('resize', handleWindowResize)
})

onUnmounted(() => {
  // 清理 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
  
  // 清理定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  
  // 移除事件监听器
  window.removeEventListener('resize', handleWindowResize)
})

// 处理窗口大小变化
const handleWindowResize = () => {
  debouncedFitToContainer()
}

// 开始编辑
const startEdit = (index: number, options?: { 
  autoZoom?: boolean
  zoomScale?: number
  padding?: number
  animationDuration?: number
}) => {
  if (editingIndex.value !== -1) {
    finishEdit()
  }
  
  editingIndex.value = index
  editingText.value = textItems[index].text
  originalText.value = textItems[index].text
  
  // 合并选项
  const finalOptions = {
    autoZoom: options?.autoZoom ?? props.autoZoomOnEdit,
    zoomScale: options?.zoomScale ?? props.editZoomScale,
    padding: options?.padding ?? props.editZoomPadding,
    animationDuration: options?.animationDuration ?? props.editZoomAnimationDuration
  }
  
  // 如果启用自动缩放，则计算并应用新的缩放和位置
  if (finalOptions.autoZoom && canvasWrapperRef.value) {
    zoomToTextItem(index, finalOptions.zoomScale, finalOptions.padding, finalOptions.animationDuration)
  }
  
  nextTick(() => {
    if (editTextarea.value && editTextarea.value[0]) {
      const textarea = editTextarea.value[0]
      textarea.focus()
      textarea.setSelectionRange(0, textarea.value.length)
    }
  })
}

// 缩放到指定文本框的方法
const zoomToTextItem = (index: number, targetScale?: number, padding?: number, animationDuration?: number) => {
  if (!canvasWrapperRef.value || index < 0 || index >= textItems.length) {
    return
  }
  
  const item = textItems[index]
  const containerRect = canvasWrapperRef.value.getBoundingClientRect()
  const containerWidth = containerRect.width
  const containerHeight = containerRect.height
  
  if (containerWidth <= 0 || containerHeight <= 0) {
    return
  }
  
  // 计算文本框在画布中的实际位置（相对于画布边界）
  const itemLeft = item.bbox[0] - canvasBounds.value.minX
  const itemTop = item.bbox[1] - canvasBounds.value.minY
  const itemWidth = item.bbox[2] - item.bbox[0]
  const itemHeight = item.bbox[3] - item.bbox[1]
  
  // 计算文本框中心点
  const itemCenterX = itemLeft + itemWidth / 2
  const itemCenterY = itemTop + itemHeight / 2
  
  // 确定最终缩放比例
  let finalScale = targetScale || props.editZoomScale
  
  // 如果没有指定缩放比例，自动计算合适的缩放比例
  if (!targetScale) {
    const paddingValue = padding || props.editZoomPadding
    const scaleX = (containerWidth - paddingValue * 2) / itemWidth
    const scaleY = (containerHeight - paddingValue * 2) / itemHeight
    finalScale = Math.min(scaleX, scaleY, props.maxScale)
  }
  
  // 限制缩放范围
  finalScale = Math.max(props.minScale, Math.min(props.maxScale, finalScale))
  
  // 计算新的平移位置，使文本框居中
  const newPanX = containerWidth / 2 - itemCenterX * finalScale
  const newPanY = containerHeight / 2 - itemCenterY * finalScale
  
  // 应用动画
  isAnimating.value = true
  scale.value = finalScale
  panX.value = newPanX
  panY.value = newPanY
  
  emit('zoom-changed', scale.value, panX.value, panY.value)
  
  // 动画结束后重置状态
  setTimeout(() => {
    isAnimating.value = false
  }, animationDuration || props.editZoomAnimationDuration)
}

// 修改 startEditById 方法，支持选项参数
const startEditById = (id: string | number, options?: { 
  autoZoom?: boolean
  zoomScale?: number
  padding?: number
  animationDuration?: number
}) => {
  const index = textItems.findIndex(item => item.id === id)
  if (index !== -1) {
    startEdit(index, options)
    return true
  }
  console.warn(`TextItem with id ${id} not found`)
  return false
}

// 只进行缩放定位，不开始编辑
const zoomToTextItemById = (id: string | number, targetScale?: number, padding?: number, animationDuration?: number) => {
  const index = textItems.findIndex(item => item.id === id)
  if (index !== -1) {
    zoomToTextItem(index, targetScale, padding, animationDuration)
    return true
  }
  console.warn(`TextItem with id ${id} not found`)
  return false
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && event.ctrlKey) {
    event.preventDefault()
    finishEdit()
  } else if (event.key === 'Escape') {
    event.preventDefault()
    cancelEdit()
  }
}

// 完成编辑
const finishEdit = () => {
  if (editingIndex.value === -1) return
  
  const index = editingIndex.value
  const item = textItems[index]
  const oldText = originalText.value
  const newText = editingText.value
  
  if (oldText !== newText) {
    textItems[index].text = newText
    
    emit('text-changed', { ...item, text: newText }, oldText, newText)
    emit('item-updated', item.id, newText, oldText)
    emit('update:textItems', [...textItems])
  }
  
  editingIndex.value = -1
  editingText.value = ''
  originalText.value = ''
}

// 取消编辑
const cancelEdit = () => {
  editingIndex.value = -1
  editingText.value = ''
  originalText.value = ''
}

// 重置文本
const resetTexts = () => {
  initializeTextItems()
  emit('update:textItems', [...textItems])
}

// 根据ID查找文本项
const findItemById = (id: string | number) => {
  return textItems.find(item => item.id === id)
}

// 根据ID更新文本项
const updateItemById = (id: string | number, newText: string) => {
  const itemIndex = textItems.findIndex(item => item.id === id)
  if (itemIndex !== -1) {
    const oldText = textItems[itemIndex].text
    textItems[itemIndex].text = newText
    emit('item-updated', id, newText, oldText)
    emit('update:textItems', [...textItems])
    return true
  }
  return false
}

// 暴露方法给父组件
defineExpose({
  updateTextById,
  getCurrentTextItems: () => [...textItems],
  resetToOriginal: () => {
    initializeTextItems()
    emit('update:textItems', [...textItems])
  },
  startEditByIndex: startEdit,
  startEditById,
  zoomToTextItem,
  zoomToTextItemById,
  findItemById,
  updateItemById,
  getChangedItems: () => {
    return textItems.filter((item, index) => {
      const original = props.textItems[index]
      return original && (item.text !== original.text)
    })
  },
  fitToContainer,
  resetZoom,
  zoomIn,
  zoomOut,
  setZoom: (newScale: number) => {
    scale.value = Math.max(props.minScale, Math.min(props.maxScale, newScale))
    emit('zoom-changed', scale.value, panX.value, panY.value)
  },
  setPan: (x: number, y: number) => {
    panX.value = x
    panY.value = y
    emit('zoom-changed', scale.value, panX.value, panY.value)
  },
  getCanvasInfo: () => ({
    width: actualCanvasWidth.value,
    height: actualCanvasHeight.value,
    bounds: canvasBounds.value
  }),
  recalculateCanvas,
  updateContainerSize,
  forceUpdateSize,
  getContainerSize: () => ({ ...containerSize })
})
</script>

<style scoped>
.text-editor-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  user-select: none;
  width: 100%;
  height: 100%;
}

.canvas-wrapper {
  background-color: #fafafa;
  background-image: 
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.canvas-container {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.canvas-bounds-indicator {
  pointer-events: none;
}

.background-overlay {
  pointer-events: none;
}

.text-item {
  transition: all 0.2s ease;
  position: relative;
  user-select: text;
}

.text-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.edit-textarea {
  font-family: inherit;
}

.text-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.control-panel {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-info {
  background-color: #17a2b8;
  color: white;
}

.btn-info:hover {
  background-color: #138496;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #218838;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.id-label {
  font-family: monospace;
}

.zoom-info, .canvas-info {
  font-family: monospace;
}
</style>
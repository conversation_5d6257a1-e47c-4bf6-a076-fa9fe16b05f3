<template>
  <div class="univer-table-wrapper">
    <div 
      ref="univerRef" 
      id="univer-container"
      class="univer-container"
      :style="containerStyle"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onBeforeUnmount, PropType, unref } from 'vue'
import { useUniverTable } from './hooks/useUniverTable'
import type { IWorkbookData } from '@univerjs/core'
import type { SheetConfig, UniverTableEmits } from './types'

defineOptions({
  name: 'UniverTable'
})

const props = defineProps({
  /** 组件宽度 */
  width: {
    type: [String, Number],
    default: '100%'
  },
  /** 组件高度 */
  height: {
    type: [String, Number],
    default: '100%'
  },
  /** 是否显示工具栏 */
  showToolbar: {
    type: Boolean,
    default: true
  },
  /** 表格数据 */
  data: {
    type: Object as PropType<Partial<IWorkbookData>>,
    default: () => ({})
  },
  /** 报表配置 */
  sheetConfig: {
    type: Object as PropType<SheetConfig>,
    default: () => ({})
  },
  /** 是否只读 */
  readonly: {
    type: Boolean,
    default: false
  },
  /** 主题 */
  theme: {
    type: String as PropType<'light' | 'dark'>,
    default: 'light'
  },
  /** 自定义CSS类名 */
  class: {
    type: String,
    default: ''
  }
})

const emit = defineEmits<UniverTableEmits>()

const univerRef = ref<HTMLElement>()

const {
  univerAPI,
  initUniver,
  destroyUniver,
  setData,
  getData,
} = useUniverTable()

// 计算容器样式
const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))


// 监听数据变化
watch(() => props.sheetConfig, (newData) => {
  if (newData) {
    setData(newData)
  }
}, { deep: true })

// 生命周期
onMounted(async () => {
  if (univerRef.value) {
    try {
      await initUniver(univerRef, props.data || {})

      if (props.sheetConfig) {
        setData(props.sheetConfig)
      }
      
      emit('ready', univerAPI.value)
    } catch (error) {
      emit('error', error as Error)
    }
  }
})

onBeforeUnmount(() => {
  destroyUniver()
})

// 暴露方法给父组件
defineExpose({
  univerAPI,
})
</script>

<style lang="scss" scoped>
.univer-table-wrapper {
  height: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;

  :deep(aside) {
    padding: 0;
  }
}

.univer-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}
</style>
import { ref, reactive, onUnmounted, Ref } from 'vue'
import { UniverSheetsCorePreset } from '@univerjs/preset-sheets-core'
import sheetsCoreZhCN from '@univerjs/preset-sheets-core/locales/zh-CN'
import { createUniver, LocaleType, mergeLocales, FUniver, Univer } from '@univerjs/presets'
import type { IWorkbookData, Workbook } from '@univerjs/core'
import type { SelectionRange, ExportOptions } from '../types'
import '@univerjs/preset-sheets-core/lib/index.css'
import { UniverSheetAdapter } from '../utils/adapter'
import { SheetConfig } from '../types'

export function useUniverTable() {
  const canUndo = ref(false)
  const canRedo = ref(false)
  const selectedRange = ref<SelectionRange | null>(null)
  const isReady = ref(false)

  let univer: Univer | null = null
  let univerAPI: FUniver | null = null
  let adapter: UniverSheetAdapter | null = null

  // 初始化 Univer
  const initUniver = async (container: Ref<HTMLElement | null>, data: Partial<IWorkbookData> = {}) => {

    // 创建 Univer 实例
    let { univer: univerInstance, univerAPI: univerAPIInstance } = createUniver({
      locale: LocaleType.ZH_CN,
      locales: {
        [LocaleType.ZH_CN]: mergeLocales(
          sheetsCoreZhCN,
        ),
      },
      presets: [
        UniverSheetsCorePreset({
          container: container.value,
        }),
      ],
    })
    univer = univerInstance
    univerAPI = univerAPIInstance
    adapter = new UniverSheetAdapter(univerAPI)

    // 创建工作簿
    univerAPI.createWorkbook(data)

    // 监听事件
    // setupEventListeners()
    
    isReady.value = true
  }

  // 设置事件监听
  const setupEventListeners = () => {
    if (!univer) return

    // 监听撤销重做状态
    const commandService = univer.getCommandService()
    const undoRedoService = univer.getUndoRedoService()

    const updateUndoRedoState = () => {
      canUndo.value = undoRedoService.canUndo()
      canRedo.value = undoRedoService.canRedo()
    }

    commandService.onCommandExecuted(() => {
      updateUndoRedoState()
    })

    // 监听选择变化
    const selectionManager = univer.getSelectionManagerService()
    selectionManager.selectionMoveEnd$.subscribe((selection) => {
      if (selection && selection.length > 0) {
        const range = selection[0].range
        selectedRange.value = {
          startRow: range.startRow,
          startCol: range.startColumn,
          endRow: range.endRow,
          endCol: range.endColumn
        }
      }
    })

    updateUndoRedoState()
  }

  // 销毁 Univer
  const destroyUniver = () => {
    univer?.dispose()
    univerAPI?.dispose()
    univer = null
    univerAPI = null
    isReady.value = false
  }

  // 设置表格数据
  const setData = (data: SheetConfig | SheetConfig[]) => {
    if (!univer) return
    adapter.clearSheet()
    if (!data) return

    if (Array.isArray(data)) {
      adapter.setBatchData(data, {
        createNewSheets: true,
      })
    } else {
      adapter.setData(data)
    }
  }

  // 获取表格数据
  const getData = (): Promise<SheetConfig | null> => {
    if (!univer) return Promise.resolve(null)

    return adapter!.getData()
  }

  // 清理资源
  onUnmounted(() => {
    destroyUniver()
  })

  return {
    univerAPI,
    canUndo,
    canRedo,
    selectedRange,
    isReady,
    initUniver,
    destroyUniver,
    setData,
    getData,
  }
}
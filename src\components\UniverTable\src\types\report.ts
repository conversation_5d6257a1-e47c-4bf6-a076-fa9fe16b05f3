

export interface CellStyle {
  fontName?: string;
  fontSize?: number;
  fontBold?: boolean;
  fontItalic?: boolean;
  fontColor?: string;
  backgroundColor?: string;
  border?: boolean;
  borderColor?: string;
  horizontalAlign?: string;
  verticalAlign?: string;
  numberFormat?: string;
}

export interface MergeCell {
  range: string;
  value?: string;
  style?: CellStyle;
}

export interface SheetConfig {
  name: string;
  data: any[]; // dict / array / mixed
  headers?: string[];
  hasHeaders: boolean;
  dataFormat: 'dict' | 'array' | 'mixed';
  mergeCells?: MergeCell[];
  freezePanes?: string;
  columnWidths?: Record<string, number>;
  rowHeights?: Record<number, number>;
  styles?: Record<string, CellStyle>;
  startRow: number;
  startCol: number;
}

export interface ReportConfig {
  templateName?: string;
  outputFilename: string; // 输出文件名
  dataSourceConfig?: Record<string, any>;
  sheets: SheetConfig[]; // 工作表配置列表
  globalStyles?: CellStyle;
  metadata?: Record<string, any>;
}
import type { IWorkbookData } from '@univerjs/core'
import { SheetConfig } from './report'

export interface UniverTableEmits {
  /** 组件准备就绪 */
  (e: 'ready', univerAPI: any): void
  /** 数据变化 */
  (e: 'change', data: any[][]): void
  /** 用户操作 */
  (e: 'action', action: TableAction): void
  /** 导出数据 */
  (e: 'export', data: Blob): void
  /** 错误事件 */
  (e: 'error', error: Error): void
  /** 选择变化 */
  (e: 'selection-change', selection: SelectionRange): void
}

export interface TableAction {
  type: 'undo' | 'redo' | 'copy' | 'paste' | 'insertRow' | 'insertColumn' | 'deleteRow' | 'deleteColumn' | 'fontFamily' | 'fontSize'
  data?: any
}

export interface SelectionRange {
  startRow: number
  startCol: number
  endRow: number
  endCol: number
}

export interface ExportOptions {
  format: 'xlsx' | 'csv' | 'pdf'
  fileName?: string
  includeStyles?: boolean
}

export interface UniverTableInstance {
  /** 获取表格数据 */
  getData: () => any[][]
  /** 设置表格数据 */
  setData: (data: any[][]) => void
  /** 撤销操作 */
  undo: () => void
  /** 重做操作 */
  redo: () => void
  /** 复制选中内容 */
  copy: () => void
  /** 粘贴内容 */
  paste: () => void
  /** 插入行 */
  insertRow: () => void
  /** 插入列 */
  insertColumn: () => void
  /** 删除行 */
  deleteRow: () => void
  /** 删除列 */
  deleteColumn: () => void
  /** 导出数据 */
  exportData: (format: ExportOptions['format']) => Promise<Blob>
}

export interface UniverCellData {
  v?: string | number | boolean; // value
  s?: any; // style
}

export interface UniverSheet {
  name: string;
  cellData: Record<number, Record<number, UniverCellData>>; // row -> col -> cell
  mergeData?: string[];
  freeze?: { xSplit: number; ySplit: number };
  columnWidth?: Record<number, number>;
  rowHeight?: Record<number, number>;
}
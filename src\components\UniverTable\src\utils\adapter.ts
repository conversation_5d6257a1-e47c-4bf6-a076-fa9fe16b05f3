import { SheetConfig } from "../types";

export function colIndexToLetter(colIndex: number) {
  // colIndex is 1-based (1 -> A)
  let n = colIndex;
  let s = '';
  while (n > 0) {
    const m = (n - 1) % 26;
    s = String.fromCharCode(65 + m) + s;
    n = Math.floor((n - 1) / 26);
  }
  return s;
}

export function letterToColIndex(colLetter: string) {
  let res = 0;
  for (let i = 0; i < colLetter.length; i++) {
    res = res * 26 + (colLetter.charCodeAt(i) - 64);
  }
  return res;
}

export function a1(row: number, col: number) {
  return `${colIndexToLetter(col)}${row}`;
}

export function rangeA1(startRow: number, startCol: number, numRows: number, numCols: number) {
  const a1Start = a1(startRow, startCol);
  const a1End = a1(startRow + Math.max(0, numRows - 1), startCol + Math.max(0, numCols - 1));
  return `${a1Start}:${a1End}`;
}

// --- Adapter 类 ---
export class UniverSheetAdapter {
  // 让调用方传入 univerAPI（或全局取 univerAPI）
  constructor(private univerAPI: any) {}

  /**
   * 处理多个 SheetConfig，可以创建多个 sheet 或覆盖多个区域
   */
  async setBatchData(sheetConfigs: SheetConfig[], options?: {
    createNewSheets?: boolean; // 是否为每个 config 创建新的 sheet
  }): Promise<void> {
    const { createNewSheets = false } = options || {};
    
    for (const config of sheetConfigs) {
      if (createNewSheets && config.name) {
        await this.createOrSelectSheet(config.name);
      }
      await this.setData(config);
    }
  }

  /**
   * 创建或选择指定名称的工作表
   * 根据文档使用正确的 API
   */
  private async createOrSelectSheet(sheetName: string): Promise<void> {
    const fWorkbook = this.univerAPI.getActiveWorkbook();
    
    try {
      // 查找已存在的工作表 - 通过遍历所有工作表查找
      const sheets = fWorkbook.getSheets();
      const existingSheet = sheets.find((sheet: any) => {
        try {
          return sheet.getName() === sheetName;
        } catch {
          return false;
        }
      });
      
      if (existingSheet) {
        fWorkbook.setActiveSheet(existingSheet);
        return;
      }
    } catch {
      // 如果获取工作表列表失败，继续创建新工作表
    }
    
    try {
      // 使用正确的 API 创建新工作表: fWorkbook.create(name, rowCount, colCount)
      const newSheet = fWorkbook.create(sheetName, 1000, 20);
      if (newSheet) {
        fWorkbook.setActiveSheet(newSheet);
      }
    } catch (e) {
      console.warn(`Failed to create sheet ${sheetName}:`, e);
    }
  }

  /**
   * 清除当前工作表的所有数据
   */
  public async clearSheet(): Promise<void> {
    try {
      const fWorkbook = this.univerAPI.getActiveWorkbook();
      
      if (!fWorkbook) {
        console.warn('No active workbook found');
        return;
      }

      // 先创建一个新的空白工作表
      const newSheet = fWorkbook.create('Sheet1', 20, 10); // 20行10列的新工作表
      newSheet.activate();
      
      // 获取所有工作表
      const sheets = fWorkbook.getSheets();
      
      // 删除除了新创建工作表之外的所有其他工作表
      for (const sheet of sheets) {
        if (sheet.getSheetId() !== newSheet.getSheetId()) {
          try {
            fWorkbook.deleteSheet(sheet);
          } catch (e) {
            console.warn(`Failed to delete sheet ${sheet.getSheetId()}:`, e);
          }
        }
      }
    } catch (e) {
      console.error('Failed to clear workbook:', e);
    }
  }

  /**
   * 把传入的 SheetConfig 写入当前活动工作表（覆盖相应区域）。
   * - data 支持：array of arrays / array of dicts / mixed（我们先把它转成二维数组）
   */
  async setData(sheetConfig: SheetConfig): Promise<void> {
    const { startRow = 1, startCol = 1 } = sheetConfig;

    // 1) 规范化 data -> 二维数组
    const { rows, finalHeaders } = this.normalizeDataToRows(sheetConfig);
    const numRows = rows.length;
    const numCols = rows.length ? Math.max(...rows.map(r => r.length)) : 0;

    // 2) 写入数据
    if (numRows > 0 && numCols > 0) {
      await this.writeDataToSheet(rows, startRow, startCol, numRows, numCols);
    }

    // 3) 处理其他配置
    await this.applySheetFormatting(sheetConfig);
  }

  /**
   * 将 SheetConfig 的 data 规范化为二维数组
   */
  private normalizeDataToRows(sheetConfig: SheetConfig): { rows: any[][], finalHeaders: string[] | undefined } {
    let rows: any[][] = [];
    let finalHeaders: string[] | undefined = sheetConfig.headers;
    
    if (!sheetConfig.data) sheetConfig.data = [];
    if (Array.isArray(sheetConfig.data) && sheetConfig.data.length === 0) {
      return { rows: [], finalHeaders };
    }

    // detect dataFormat first if not provided
    const first = Array.isArray(sheetConfig.data) ? sheetConfig.data[0] : null;
    const dataFormat = sheetConfig.dataFormat || (first && typeof first === 'object' && !Array.isArray(first) ? 'dict' : 'array');

    switch (dataFormat) {
      case 'dict':
        return this.processDictData(sheetConfig, finalHeaders);
      case 'array':
        return this.processArrayData(sheetConfig, finalHeaders);
      default: // mixed
        return this.processMixedData(sheetConfig, finalHeaders);
    }
  }

  /**
   * 处理字典格式的数据
   */
  private processDictData(sheetConfig: SheetConfig, finalHeaders: string[] | undefined): { rows: any[][], finalHeaders: string[] | undefined } {
    // 如果没有 headers，自动从所有对象键中取顺序
    if (!finalHeaders || finalHeaders.length === 0) {
      if (Array.isArray(sheetConfig.data) && sheetConfig.data[0]) {
        const allKeys = new Set<string>();
        const firstKeys = Object.keys(sheetConfig.data[0]);
        
        // 先添加第一个对象的键（保持顺序）
        firstKeys.forEach(key => allKeys.add(key));
        
        // 然后添加其他对象中可能存在的额外键
        sheetConfig.data.forEach((obj: any) => {
          if (obj && typeof obj === 'object') {
            Object.keys(obj).forEach(key => allKeys.add(key));
          }
        });
        
        finalHeaders = Array.from(allKeys);
      } else {
        finalHeaders = [];
      }
    }
    
    // 确保 sheetConfig.headers 被正确设置
    sheetConfig.headers = finalHeaders;
    
    const rows = (sheetConfig.data as any[]).map((obj: any) => finalHeaders!.map(h => obj?.[h] ?? null));
    
    // 如果设置了 hasHeaders 为 true，需要在数据前面添加 header 行
    if (sheetConfig.hasHeaders && finalHeaders && finalHeaders.length > 0) {
      rows.unshift(finalHeaders);
    }

    return { rows, finalHeaders };
  }

  /**
   * 处理数组格式的数据
   */
  private processArrayData(sheetConfig: SheetConfig, finalHeaders: string[] | undefined): { rows: any[][], finalHeaders: string[] | undefined } {
    // normalize: 如果是一维（每项非数组），则把每项放成一行单列
    let rows: any[][];
    if (Array.isArray(sheetConfig.data[0]) || Array.isArray(sheetConfig.data)) {
      // ensure each entry is an array
      rows = (sheetConfig.data as any[]).map(r => Array.isArray(r) ? r : [r]);
    } else {
      rows = (sheetConfig.data as any[]).map(r => [r]);
    }
    
    // 如果设置了 hasHeaders 为 true 且提供了 headers，需要在数据前面添加 header 行
    if (sheetConfig.hasHeaders && finalHeaders && finalHeaders.length > 0) {
      rows.unshift(finalHeaders);
    }

    return { rows, finalHeaders };
  }

  /**
   * 处理混合格式的数据
   */
  private processMixedData(sheetConfig: SheetConfig, finalHeaders: string[] | undefined): { rows: any[][], finalHeaders: string[] | undefined } {
    const processedRows: any[][] = [];
    
    (sheetConfig.data as any[]).forEach(r => {
      if (r && typeof r === 'object' && !Array.isArray(r)) {
        // 处理对象类型的行
        if (!finalHeaders || finalHeaders.length === 0) {
          finalHeaders = Object.keys(r);
          sheetConfig.headers = finalHeaders;
        }
        processedRows.push(finalHeaders.map(h => r[h] ?? null));
      } else if (Array.isArray(r)) {
        processedRows.push(r);
      } else {
        processedRows.push([r]);
      }
    });
    
    const rows = processedRows;
    
    // 如果设置了 hasHeaders 为 true 且提供了 headers，需要在数据前面添加 header 行
    if (sheetConfig.hasHeaders && finalHeaders && finalHeaders.length > 0) {
      rows.unshift(finalHeaders);
    }

    return { rows, finalHeaders };
  }

  /**
   * 将数据写入工作表
   * 使用正确的 0-based 坐标系统和 setValues API
   */
  private async writeDataToSheet(rows: any[][], startRow: number, startCol: number, numRows: number, numCols: number): Promise<void> {
    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = fWorkbook.getActiveSheet();

    try {
      // 使用 0-based 坐标系统 (文档中明确说明 getRange 使用 0-based)
      const fRange = fWorksheet.getRange(startRow - 1, startCol - 1, numRows, numCols);

      // 构造值矩阵（多维数组）
      const values = rows.map(r => {
        // pad row 到 numCols
        const padded = Array.from({ length: numCols }, (_, i) => (i < r.length ? r[i] : null));
        return padded;
      });

      // 使用文档中的 setValues 方法
      fRange.setValues(values);
    } catch (e) {
      console.warn('Failed to write data to sheet:', e);
      // Fallback: 尝试使用 A1 notation
      try {
        const a1Range = rangeA1(startRow, startCol, numRows, numCols);
        const fRange = fWorksheet.getRange(a1Range);
        const values = rows.map(r => {
          const padded = Array.from({ length: numCols }, (_, i) => (i < r.length ? r[i] : null));
          return padded;
        });
        fRange.setValues(values);
      } catch (e2) {
        console.warn('Fallback write also failed:', e2);
      }
    }
  }

  /**
   * 应用工作表格式化（合并单元格、冻结窗格、列宽行高等）
   */
  private async applySheetFormatting(sheetConfig: SheetConfig): Promise<void> {
    // 处理合并单元格
    await this.applyMergeCells(sheetConfig.mergeCells);
    
    // 处理冻结窗格
    await this.applyFreezePanes(sheetConfig.freezePanes);
    
    // 处理列宽和行高
    await this.applyColumnWidths(sheetConfig.columnWidths);
    await this.applyRowHeights(sheetConfig.rowHeights);
  }

  /**
   * 应用合并单元格
   * 使用文档中的 merge() API
   */
  private async applyMergeCells(mergeCells?: any[]): Promise<void> {
    if (!mergeCells || !Array.isArray(mergeCells)) return;

    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = fWorkbook.getActiveSheet();

    for (const m of mergeCells) {
      try {
        // 使用 A1 notation 获取范围并合并
        const mergeRange = fWorksheet.getRange(m.range);
        mergeRange.merge(); // 使用文档中的 merge() 方法
        
        // 合并后把值写到左上角（如果提供）
        if (m.value !== undefined && m.value !== null) {
          const topLeft = m.range.split(':')[0];
          const topLeftRange = fWorksheet.getRange(topLeft);
          topLeftRange.setValue(m.value);
        }
      } catch (e) {
        console.warn('merge failed for', m.range, e);
      }
    }
  }

  /**
   * 应用冻结窗格
   * 根据文档修正冻结 API 调用
   */
  private async applyFreezePanes(freezePanes?: string): Promise<void> {
    if (!freezePanes) return;

    try {
      const fWorkbook = this.univerAPI.getActiveWorkbook();
      const fWorksheet = fWorkbook.getActiveSheet();
      
      // 支持 'B2' 或 '2,1' 或 'row,col'
      let freezeAt: string | null = null;
      if (/^[A-Za-z]+\d+$/.test(freezePanes)) {
        freezeAt = freezePanes;
      } else if (/^\d+\s*,\s*\d+$/.test(freezePanes)) {
        const [r, c] = freezePanes.split(',').map(s => parseInt(s, 10));
        freezeAt = a1(r, c);
      }
      
      if (freezeAt) {
        // 根据文档，冻结 API 可能因版本而异，尝试多种方法
        try {
          // 方法1: 如果存在 setFreeze 方法
          if (typeof (fWorksheet as any).setFreeze === 'function') {
            const row = parseInt(freezeAt.replace(/[^0-9]/g, ''), 10);
            const col = letterToColIndex(freezeAt.replace(/[0-9]/g, ''));
            (fWorksheet as any).setFreeze({ row, column: col });
          }
          // 方法2: 如果存在 freezeAt 方法  
          else if (typeof (fWorksheet as any).freezeAt === 'function') {
            (fWorksheet as any).freezeAt(freezeAt);
          }
        } catch (e) {
          console.warn('Failed to apply freeze with methods:', e);
        }
      }
    } catch (e) {
      console.warn('Failed to apply freeze panes:', e);
    }
  }

  /**
   * 应用列宽
   * 根据文档修正列宽设置 API
   */
  private async applyColumnWidths(columnWidths?: Record<string, number>): Promise<void> {
    if (!columnWidths) return;

    try {
      const fWorkbook = this.univerAPI.getActiveWorkbook();
      const fWorksheet = fWorkbook.getActiveSheet();

      for (const [colLetter, width] of Object.entries(columnWidths)) {
        try {
          // 使用列范围设置列宽
          const colRange = fWorksheet.getRange(`${colLetter}:${colLetter}`);
          
          // 尝试不同的列宽设置方法
          if (typeof (colRange as any).setColumnWidth === 'function') {
            (colRange as any).setColumnWidth(width);
          } else {
            // Fallback: 直接在工作表上设置
            const colIndex = letterToColIndex(colLetter);
            if (typeof (fWorksheet as any).setColumnWidth === 'function') {
              (fWorksheet as any).setColumnWidth(colIndex - 1, width); // 转为 0-based
            }
          }
        } catch (e) {
          console.warn(`Failed to set column width for ${colLetter}:`, e);
        }
      }
    } catch (e) {
      console.warn('Failed to apply column widths:', e);
    }
  }

  /**
   * 应用行高
   * 根据文档修正行高设置 API
   */
  private async applyRowHeights(rowHeights?: Record<string, number>): Promise<void> {
    if (!rowHeights) return;

    try {
      const fWorkbook = this.univerAPI.getActiveWorkbook();
      const fWorksheet = fWorkbook.getActiveSheet();

      for (const [rStr, height] of Object.entries(rowHeights)) {
        try {
          const r = parseInt(rStr, 10);
          // 使用行范围设置行高
          const rowRange = fWorksheet.getRange(`${r}:${r}`);
          
          // 尝试不同的行高设置方法
          if (typeof (rowRange as any).setRowHeight === 'function') {
            (rowRange as any).setRowHeight(height);
          } else {
            // Fallback: 直接在工作表上设置
            if (typeof (fWorksheet as any).setRowHeight === 'function') {
              (fWorksheet as any).setRowHeight(r - 1, height); // 转为 0-based
            }
          }
        } catch (e) {
          console.warn(`Failed to set row height for row ${rStr}:`, e);
        }
      }
    } catch (e) {
      console.warn('Failed to apply row heights:', e);
    }
  }

  /**
   * 从当前活动工作表读取数据并封装成 SheetConfig
   * 使用正确的 getValues() API
   */
  async getData(): Promise<SheetConfig> {
    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const fWorksheet = fWorkbook.getActiveSheet();

    // 1) 获取 used range 的值
    const values = await this.getUsedRangeValues(fWorksheet);

    // 2) 构造 SheetConfig
    return this.buildSheetConfigFromValues(fWorksheet, values);
  }

  /**
   * 从多个工作表读取数据
   */
  async getBatchData(sheetNames?: string[]): Promise<SheetConfig[]> {
    const fWorkbook = this.univerAPI.getActiveWorkbook();
    const configs: SheetConfig[] = [];

    if (sheetNames) {
      // 读取指定的工作表
      for (const sheetName of sheetNames) {
        try {
          // 通过遍历查找工作表
          const sheets = fWorkbook.getSheets();
          const sheet = sheets.find((s: any) => {
            try {
              return s.getName() === sheetName;
            } catch {
              return false;
            }
          });
          
          if (sheet) {
            const originalActiveSheet = fWorkbook.getActiveSheet();
            fWorkbook.setActiveSheet(sheet);
            const config = await this.getData();
            configs.push(config);
            fWorkbook.setActiveSheet(originalActiveSheet);
          }
        } catch (e) {
          console.warn(`Failed to read sheet ${sheetName}:`, e);
        }
      }
    } else {
      // 读取所有工作表
      try {
        const allSheets = fWorkbook.getSheets();
        const originalActiveSheet = fWorkbook.getActiveSheet();
        
        for (const sheet of allSheets) {
          try {
            fWorkbook.setActiveSheet(sheet);
            const config = await this.getData();
            configs.push(config);
          } catch (e) {
            console.warn('Failed to read a sheet:', e);
          }
        }
        
        fWorkbook.setActiveSheet(originalActiveSheet);
      } catch (e) {
        console.warn('Failed to get all sheets:', e);
        // fallback: 至少读取当前活动工作表
        const config = await this.getData();
        configs.push(config);
      }
    }

    return configs;
  }

  /**
   * 获取工作表的使用区域值
   * 使用文档中的 getValues() API
   */
  private async getUsedRangeValues(fWorksheet: any): Promise<any[][]> {
    try {
      // 尝试使用大范围获取数据，然后过滤空行
      const fRange = fWorksheet.getRange('A1:Z1000');
      const values = fRange.getValues(); // 使用文档中的 getValues() 方法
      
      // 过滤掉完全空的行
      const nonEmptyRows: any[][] = [];
      for (const row of values) {
        if (row.some((cell: any) => cell !== null && cell !== undefined && cell !== '')) {
          nonEmptyRows.push(row);
        }
      }
      
      return nonEmptyRows;
    } catch (e) {
      console.warn('Failed to get range values:', e);
      return [];
    }
  }

  /**
   * 从值数组构建 SheetConfig
   */
  private buildSheetConfigFromValues(fWorksheet: any, values: any[][]): SheetConfig {
    const numRows = values.length;
    const numCols = values[0] ? values[0].length : 0;

    // 猜测是否有 headers：如果第一行全为 string 且非空，则作为 headers
    let hasHeaders = false;
    let headers: string[] | undefined = undefined;
    let data: any[] = [];
    
    if (numRows > 0) {
      const firstRow = values[0];
      const allString = firstRow.every((c: any) => typeof c === 'string' && c.trim() !== '');
      const hasDataRows = numRows > 1 && values.slice(1).some((row: any[]) => row.some(cell => cell !== null && cell !== undefined && cell !== ''));
      
      if (allString && hasDataRows) {
        hasHeaders = true;
        headers = firstRow.map((s: any) => String(s).trim());
        
        // convert to dict array：从第二行开始
        for (let r = 1; r < numRows; r++) {
          const row = values[r];
          // 跳过完全空的行
          if (row.every((cell: any) => cell === null || cell === undefined || cell === '')) {
            continue;
          }
          
          const obj: any = {};
          for (let c = 0; c < Math.min(numCols, headers.length); c++) {
            obj[headers[c]] = row[c];
          }
          data.push(obj);
        }
      } else {
        // return raw 2D array，过滤掉完全空的行
        hasHeaders = false;
        data = values.filter((row: any[]) => 
          row.some(cell => cell !== null && cell !== undefined && cell !== '')
        );
      }
    }

    const sheetConfig: SheetConfig = {
      name: this.getWorksheetName(fWorksheet),
      data,
      headers,
      hasHeaders,
      dataFormat: hasHeaders ? 'dict' : 'array',
      mergeCells: [], // TODO: 如果需要合并信息，需要使用更复杂的 API
      freezePanes: undefined,
      columnWidths: {},
      rowHeights: {},
      styles: {},
      startRow: 1,
      startCol: 1,
    };

    // 尝试获取 freeze 信息
    this.addFreezeInfoToConfig(fWorksheet, sheetConfig);

    return sheetConfig;
  }

  /**
   * 获取工作表名称
   * 使用正确的 getName() API
   */
  private getWorksheetName(fWorksheet: any): string {
    try {
      // 根据文档，应该使用 getName() 方法
      if (typeof fWorksheet.getName === 'function') {
        return fWorksheet.getName();
      }
      // Fallback
      return fWorksheet.getSheetName?.() || 'Sheet1';
    } catch {
      return 'Sheet1';
    }
  }

  /**
   * 添加冻结信息到配置中
   */
  private addFreezeInfoToConfig(fWorksheet: any, sheetConfig: SheetConfig): void {
    try {
      if (typeof (fWorksheet as any).getFreeze === 'function') {
        const freeze = (fWorksheet as any).getFreeze();
        if (freeze) {
          sheetConfig.freezePanes = JSON.stringify(freeze);
        }
      }
    } catch {
      // 忽略获取冻结信息的错误
    }
  }
}
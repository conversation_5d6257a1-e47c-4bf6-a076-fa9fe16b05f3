import * as XLSX from 'xlsx'

/**
 * 格式化表格数据
 */
export function formatTableData(data: any[][]): any[][] {
  if (!Array.isArray(data) || data.length === 0) {
    return []
  }

  return data.map(row => {
    if (!Array.isArray(row)) {
      return [row]
    }
    return row.map(cell => {
      // 处理空值
      if (cell === null || cell === undefined) {
        return ''
      }
      
      // 处理数字
      if (typeof cell === 'number') {
        return cell
      }
      
      // 处理布尔值
      if (typeof cell === 'boolean') {
        return cell.toString()
      }
      
      // 处理日期
      if (cell instanceof Date) {
        return cell.toISOString().split('T')[0]
      }
      
      // 处理对象
      if (typeof cell === 'object') {
        return JSON.stringify(cell)
      }
      
      // 默认转为字符串
      return String(cell)
    })
  })
}

/**
 * 验证表格数据
 */
export function validateTableData(data: any[][]): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!Array.isArray(data)) {
    errors.push('数据必须是数组格式')
    return { valid: false, errors }
  }
  
  if (data.length === 0) {
    errors.push('数据不能为空')
    return { valid: false, errors }
  }
  
  // 检查每行是否为数组
  data.forEach((row, index) => {
    if (!Array.isArray(row)) {
      errors.push(`第${index + 1}行数据格式错误，应为数组`)
    }
  })
  
  return { valid: errors.length === 0, errors }
}

/**
 * 导出为 Excel 文件
 */
export function exportToExcel(data: any[][], fileName = 'table-data.xlsx'): Blob {
  const worksheet = XLSX.utils.aoa_to_sheet(data)
  const workbook = XLSX.utils.book_new()
  
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
  
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
  return new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
}

/**
 * 导出为 CSV 文件
 */
export function exportToCsv(data: any[][], fileName = 'table-data.csv'): Blob {
  const csvContent = data.map(row => 
    row.map(cell => {
      // 处理包含逗号、换行符或引号的内容
      const cellStr = String(cell || '')
      if (cellStr.includes(',') || cellStr.includes('\n') || cellStr.includes('"')) {
        return `"${cellStr.replace(/"/g, '""')}"`
      }
      return cellStr
    }).join(',')
  ).join('\n')
  
  // 添加 BOM 以支持中文
  const bom = '\uFEFF'
  return new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8' })
}

/**
 * 从 CSV 解析数据
 */
export function parseCsvData(csvText: string): any[][] {
  const lines = csvText.split('\n')
  const result: any[][] = []
  
  for (const line of lines) {
    if (line.trim() === '') continue
    
    const row: any[] = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"' && inQuotes && line[i + 1] === '"') {
        current += '"'
        i++ // 跳过下一个引号
      } else if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        row.push(parseCell(current))
        current = ''
      } else {
        current += char
      }
    }
    
    row.push(parseCell(current))
    result.push(row)
  }
  
  return result
}

/**
 * 解析单元格数据
 */
function parseCell(value: string): any {
  const trimmed = value.trim()
  
  // 空值
  if (trimmed === '') {
    return ''
  }
  
  // 数字
  if (/^-?\d+\.?\d*$/.test(trimmed)) {
    return parseFloat(trimmed)
  }
  
  // 布尔值
  if (trimmed.toLowerCase() === 'true') {
    return true
  }
  if (trimmed.toLowerCase() === 'false') {
    return false
  }
  
  // 日期
  if (/^\d{4}-\d{2}-\d{2}$/.test(trimmed)) {
    return new Date(trimmed)
  }
  
  return trimmed
}

/**
 * 创建示例数据
 */
export function createSampleData(rows = 10, cols = 5): any[][] {
  const data: any[][] = []
  
  // 添加表头
  const headers = Array.from({ length: cols }, (_, i) => `列${i + 1}`)
  data.push(headers)
  
  // 添加数据行
  for (let i = 1; i <= rows; i++) {
    const row = Array.from({ length: cols }, (_, j) => {
      if (j === 0) return `行${i}`
      if (j === 1) return Math.floor(Math.random() * 1000)
      if (j === 2) return Math.random() > 0.5
      if (j === 3) return new Date(2024, 0, i)
      return `数据${i}-${j}`
    })
    data.push(row)
  }
  
  return data
}

/**
 * 数据统计工具
 */
export function calculateStats(data: any[][], columnIndex: number) {
  const values = data.slice(1).map(row => row[columnIndex]).filter(val => typeof val === 'number')
  
  if (values.length === 0) {
    return { count: 0, sum: 0, avg: 0, min: 0, max: 0 }
  }
  
  const sum = values.reduce((acc, val) => acc + val, 0)
  const avg = sum / values.length
  const min = Math.min(...values)
  const max = Math.max(...values)
  
  return { count: values.length, sum, avg, min, max }
}
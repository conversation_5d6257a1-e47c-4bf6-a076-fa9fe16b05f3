// src/directive/common/lazyLoad.js
export default {
  mounted(el, binding) {
    // binding.value 应该是一个函数
    const cb = binding.value;
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        cb();
        observer.unobserve(el);
      }
    });
    observer.observe(el);
    el._lazyObserver = observer;
  },
  unmounted(el) {
    if (el._lazyObserver) {
      el._lazyObserver.disconnect();
      delete el._lazyObserver;
    }
  }
};
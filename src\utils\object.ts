type AnyObject = { [key: string]: any }

/**
 * 根据前缀分组枚举值
 */
export function groupByPrefixes(enumObj: any, prefixes: readonly string[]) {
  const result: Record<string, string[]> = {};

  prefixes.forEach(prefix => {
    result[prefix] = Object.entries(enumObj)
      .filter(([key]) => key.startsWith(prefix))
      .map(([, value]) => value as string);
  });

  return result;
}

/**
 * 确保对象的指定属性是数组，并往其中添加值
 * @param obj 任意对象
 * @param key 属性名
 * @param value 要添加的值
 */
export function pushToArrayProp<T extends object, K extends keyof any>(
  obj: T,
  key: K,
  value: any
) {
  // 如果属性不存在，初始化为空数组
  if (!(key in obj)) {
    (obj as any)[key] = [];
  }

  // 确保是数组再 push
  if (Array.isArray((obj as any)[key])) {
    (obj as any)[key].push(value);
  } else {
    throw new Error(`属性 ${String(key)} 已存在，但不是数组`);
  }
}

/**
 * 更新嵌套对象中的第一个匹配对象
 */
export function updateFirstMatchObject<T extends AnyObject>(
  obj: AnyObject,
  matchKey: string,
  matchValue: any,
  updater: Partial<T>
): boolean {
  if (obj && typeof obj === "object") {
    // 判断是否满足条件
    if (obj[matchKey] === matchValue) {
      Object.assign(obj, updater) // 直接修改当前对象
      return true
    }

    // 遍历子对象
    for (const key of Object.keys(obj)) {
      if (updateFirstMatchObject<T>(obj[key], matchKey, matchValue, updater)) {
        return true // 找到就停止
      }
    }
  }
  return false
}

/**
 * 更新嵌套对象中的所有匹配对象
 */
export function updateAllMatchObjects<T extends AnyObject>(
  obj: AnyObject,
  matchKey: string,
  matchValue: any,
  updater: Partial<T>
): number {
  let count = 0

  if (obj && typeof obj === "object") {
    if (obj[matchKey] === matchValue) {
      Object.assign(obj, updater) // 直接修改当前对象
      count++
    }

    for (const key of Object.keys(obj)) {
      count += updateAllMatchObjects<T>(obj[key], matchKey, matchValue, updater)
    }
  }

  return count
}

/**
 * 更新嵌套对象中的第一个匹配项
 */
export function updateFirstMatch<T extends AnyObject>(
  obj: AnyObject,
  matchKey: string,
  matchValue: any,
  targetKey: string,
  updater: Partial<T>
): boolean {
  if (obj && typeof obj === "object") {
    // 判断是否满足条件
    if (obj[matchKey] === matchValue && targetKey in obj) {
      const target = obj[targetKey]
      if (target && typeof target === "object") {
        Object.assign(target, updater) // 浅合并更新
        return true
      }
    }

    // 遍历子对象
    for (const key of Object.keys(obj)) {
      if (updateFirstMatch<T>(obj[key], matchKey, matchValue, targetKey, updater)) {
        return true // 找到就停止
      }
    }
  }
  return false
}

/**
 * 更新嵌套对象中的所有匹配项
 */
export function updateAllMatches<T extends AnyObject>(
  obj: AnyObject,
  matchKey: string,
  matchValue: any,
  targetKey: string,
  updater: Partial<T>
): number {
  let count = 0

  if (obj && typeof obj === "object") {
    if (obj[matchKey] === matchValue && targetKey in obj) {
      const target = obj[targetKey]
      if (target && typeof target === "object") {
        Object.assign(target, updater)
        count++
      }
    }

    for (const key of Object.keys(obj)) {
      count += updateAllMatches<T>(obj[key], matchKey, matchValue, targetKey, updater)
    }
  }

  return count
}

/**
 * 结构校验：判断 obj 是否至少包含 Object 的所有 key
 */
export function isLikeObject<T extends AnyObject>(obj: any, keys: (keyof T)[]): obj is T {
  return obj && typeof obj === "object" && keys.every(k => k in obj)
}

/**
 * 更新嵌套对象中的第一个匹配项（结构匹配）
 */
export function updateFirstMatchByShape<T extends AnyObject>(
  obj: AnyObject,
  matchKey: string,
  matchValue: any,
  shapeKeys: (keyof T)[],
  updater: Partial<T>
): boolean {
  if (obj && typeof obj === "object") {
    if (obj[matchKey] === matchValue) {
      for (const [k, v] of Object.entries(obj)) {
        if (isLikeObject<T>(v, shapeKeys)) {
          Object.assign(v, updater)
          return true
        }
      }
    }

    for (const key of Object.keys(obj)) {
      if (updateFirstMatchByShape<T>(obj[key], matchKey, matchValue, shapeKeys, updater)) {
        return true
      }
    }
  }
  return false
}

/**
 * 更新嵌套对象中的所有匹配项（结构匹配）
 */
export function updateAllMatchesByShape<T extends AnyObject>(
  obj: AnyObject,
  matchKey: string,
  matchValue: any,
  shapeKeys: (keyof T)[],
  updater: Partial<T>
): number {
  let count = 0

  if (obj && typeof obj === "object") {
    if (obj[matchKey] === matchValue) {
      for (const [k, v] of Object.entries(obj)) {
        if (isLikeObject<T>(v, shapeKeys)) {
          Object.assign(v, updater)
          count++
        }
      }
    }

    for (const key of Object.keys(obj)) {
      count += updateAllMatchesByShape<T>(obj[key], matchKey, matchValue, shapeKeys, updater)
    }
  }

  return count
}


/**
 * 删除嵌套结构中数组里的第一个匹配项
 */
export function deleteFirstMatch(
  obj: AnyObject,
  matchKey: string,
  matchValue: any
): boolean {
  if (Array.isArray(obj)) {
    for (let i = 0; i < obj.length; i++) {
      const item = obj[i]
      if (item && typeof item === "object" && item[matchKey] === matchValue) {
        obj.splice(i, 1) // 删除这个对象
        return true
      }
      if (deleteFirstMatch(item, matchKey, matchValue)) {
        return true
      }
    }
  } else if (obj && typeof obj === "object") {
    for (const key of Object.keys(obj)) {
      if (deleteFirstMatch(obj[key], matchKey, matchValue)) {
        return true
      }
    }
  }
  return false
}

/**
 * 删除嵌套结构中数组里的所有匹配项
 */
export function deleteAllMatches(
  obj: AnyObject,
  matchKey: string,
  matchValue: any
): number {
  let count = 0

  if (Array.isArray(obj)) {
    for (let i = obj.length - 1; i >= 0; i--) {
      const item = obj[i]
      if (item && typeof item === "object" && item[matchKey] === matchValue) {
        obj.splice(i, 1)
        count++
      } else {
        count += deleteAllMatches(item, matchKey, matchValue)
      }
    }
  } else if (obj && typeof obj === "object") {
    for (const key of Object.keys(obj)) {
      count += deleteAllMatches(obj[key], matchKey, matchValue)
    }
  }

  return count
}
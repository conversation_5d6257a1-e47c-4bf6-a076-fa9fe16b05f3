export function createWebSocket(url) {
  // 拼接完整的WebSocket URL
  if (!url.startsWith('ws://') && !url.startsWith('wss://')) {
    if (!url.startsWith('/')) url = '/' + url
    url = import.meta.env.VITE_APP_WS_API + url
  }
  console.log("创建WebSocket连接，URL:", url)
  return new WebSocket(url)
}

export class WebSocketManager {
  constructor(options = {}) {
    this.options = {
      heartbeatInterval: 30000, // 心跳间隔，默认30秒
      reconnectInterval: 5000,  // 重连间隔，默认5秒
      maxReconnectAttempts: 3,  // 最大重连次数
      heartbeatMessage: JSON.stringify({ type: 'ping' }), // 心跳消息
      ...options
    };
    
    this.connections = new Map(); // 存储所有连接
  }

  /**
   * 创建WebSocket连接
   * @param {string} socketId - 唯一标识符
   * @param {string} url - WebSocket URL
   * @param {Object} handlers - 事件处理器
   * @param {Function} handlers.onMessage - 消息处理函数
   * @param {Function} handlers.onError - 错误处理函数
   * @param {Function} handlers.onClose - 关闭处理函数
   * @param {Function} handlers.onOpen - 连接打开处理函数
   */
  connect(socketId, url, handlers = {}) {
    // 如果已存在连接，先关闭
    if (this.connections.has(socketId)) {
      this.disconnect(socketId);
    }

    const connectionInfo = {
      socketId,
      url,
      handlers,
      socket: null,
      heartbeatTimer: null,
      reconnectTimer: null,
      reconnectAttempts: 0,
      isManualClose: false
    };

    this.connections.set(socketId, connectionInfo);
    this._createConnection(connectionInfo);
  }

  /**
   * 创建实际的WebSocket连接
   * @private
   */
  _createConnection(connectionInfo) {
    try {
      const socket = createWebSocket(connectionInfo.url);
      connectionInfo.socket = socket;

      socket.onopen = (event) => {
        connectionInfo.reconnectAttempts = 0;
        this._startHeartbeat(connectionInfo);
        
        if (connectionInfo.handlers.onOpen) {
          connectionInfo.handlers.onOpen(event, connectionInfo.socketId);
        }
      };

      socket.onmessage = (event) => {
        this._handleMessage(connectionInfo, event);
      };

      socket.onerror = (error) => {
        console.error(`WebSocket 连接错误（socketId=${connectionInfo.socketId}）:`, error);
        this._stopHeartbeat(connectionInfo);
        
        if (connectionInfo.handlers.onError) {
          connectionInfo.handlers.onError(error, connectionInfo.socketId);
        }
      };

      socket.onclose = (event) => {
        console.warn(`WebSocket 已关闭（socketId=${connectionInfo.socketId}）:`, event);
        this._stopHeartbeat(connectionInfo);
        
        if (connectionInfo.handlers.onClose) {
          connectionInfo.handlers.onClose(event, connectionInfo.socketId);
        }

        // 如果不是手动关闭且未达到最大重连次数，尝试重连
        if (!connectionInfo.isManualClose && 
            connectionInfo.reconnectAttempts < this.options.maxReconnectAttempts) {
          this._scheduleReconnect(connectionInfo);
        }
      };

    } catch (err) {
      console.error(`初始化 WebSocket 失败（socketId=${connectionInfo.socketId}）:`, err);
      if (connectionInfo.handlers.onError) {
        connectionInfo.handlers.onError(err, connectionInfo.socketId);
      }
    }
  }

  /**
   * 处理接收到的消息
   * @private
   */
  _handleMessage(connectionInfo, event) {
    let data;
    try {
      data = JSON.parse(event.data);
    } catch (err) {
      console.error(`JSON 解析失败（socketId=${connectionInfo.socketId}）:`, err, '原始数据:', event.data);
      return;
    }

    // 处理心跳响应
    if (data.type === 'pong') {
      console.log(`收到心跳响应（socketId=${connectionInfo.socketId}）`);
      return;
    }

    // 调用用户定义的消息处理器
    if (connectionInfo.handlers.onMessage) {
      connectionInfo.handlers.onMessage(data, connectionInfo.socketId);
    }
  }

  /**
   * 启动心跳
   * @private
   */
  _startHeartbeat(connectionInfo) {
    this._stopHeartbeat(connectionInfo); // 先清除之前的定时器
    
    connectionInfo.heartbeatTimer = setInterval(() => {
      if (connectionInfo.socket && connectionInfo.socket.readyState === WebSocket.OPEN) {
        connectionInfo.socket.send(this.options.heartbeatMessage);
        console.log(`发送心跳（socketId=${connectionInfo.socketId}）`);
      }
    }, this.options.heartbeatInterval);
  }

  /**
   * 停止心跳
   * @private
   */
  _stopHeartbeat(connectionInfo) {
    if (connectionInfo.heartbeatTimer) {
      clearInterval(connectionInfo.heartbeatTimer);
      connectionInfo.heartbeatTimer = null;
    }
  }

  /**
   * 安排重连
   * @private
   */
  _scheduleReconnect(connectionInfo) {
    connectionInfo.reconnectAttempts++;
    console.log(`准备重连（socketId=${connectionInfo.socketId}），第 ${connectionInfo.reconnectAttempts} 次尝试`);
    
    connectionInfo.reconnectTimer = setTimeout(() => {
      this._createConnection(connectionInfo);
    }, this.options.reconnectInterval);
  }

  /**
   * 断开指定连接
   */
  disconnect(socketId) {
    const connectionInfo = this.connections.get(socketId);
    if (connectionInfo) {
      connectionInfo.isManualClose = true;
      this._stopHeartbeat(connectionInfo);
      
      if (connectionInfo.reconnectTimer) {
        clearTimeout(connectionInfo.reconnectTimer);
      }
      
      if (connectionInfo.socket) {
        connectionInfo.socket.close();
      }
      
      this.connections.delete(socketId);
      console.log(`已断开连接（socketId=${socketId}）`);
    }
  }

  /**
   * 断开所有连接
   */
  disconnectAll() {
    for (const socketId of this.connections.keys()) {
      this.disconnect(socketId);
    }
  }

  /**
   * 发送消息到指定连接
   */
  send(socketId, message) {
    const connectionInfo = this.connections.get(socketId);
    if (connectionInfo && connectionInfo.socket && connectionInfo.socket.readyState === WebSocket.OPEN) {
      connectionInfo.socket.send(typeof message === 'string' ? message : JSON.stringify(message));
      return true;
    }
    return false;
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(socketId) {
    const connectionInfo = this.connections.get(socketId);
    if (!connectionInfo || !connectionInfo.socket) {
      return 'DISCONNECTED';
    }
    
    switch (connectionInfo.socket.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'OPEN';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'CLOSED';
      default: return 'UNKNOWN';
    }
  }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager({
  heartbeatInterval: 30000,
  reconnectInterval: 5000,
  maxReconnectAttempts: 3
});

export default wsManager;
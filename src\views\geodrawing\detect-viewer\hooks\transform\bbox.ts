import { Box, DetectLabel, VisLabelMap } from '../../types'

/********************* 剖面 *********************/
// 从剖面信息构建边界框
const buildBboxesFromSectionInfo = (section): Box[] => {
  const bboxes: Box[] = []
  if (!section) {
    return bboxes
  }
  // 剖面
  bboxes.push(new Box(
    section.id,
    section.className,
    section.boundingBox?.x,
    section.boundingBox?.y,
    section.boundingBox?.width,
    section.boundingBox?.height,
    section.isDelete
  ))
  // 刻度尺
  const ruler = section.ruler
  if (ruler) {
    bboxes.push(new Box(
      ruler.id,
      ruler.className,
      ruler.boundingBox?.x,
      ruler.boundingBox?.y,
      ruler.boundingBox?.width,
      ruler.boundingBox?.height,
      ruler.isDelete
    ))
  }
  // 剖面名称本生的 bbox
  const sectionNumber = section.sectionNumber
  if (sectionNumber) {
    bboxes.push(new Box(
      sectionNumber.id,
      sectionNumber.className,
      sectionNumber.boundingBox?.x,
      sectionNumber.boundingBox?.y,
      sectionNumber.boundingBox?.width,
      sectionNumber.boundingBox?.height,
      sectionNumber.isDelete
    ))
  }
  // 钻口
  section.drillHoles?.forEach(hole => {
    // 钻口的 bbox
    bboxes.push(new Box(
      hole.id,
      hole.className,
      hole.boundingBox?.x,
      hole.boundingBox?.y,
      hole.boundingBox?.width,
      hole.boundingBox?.height,
      hole.isDelete
    ))
    // 采样点的 bbox
    hole.samples?.forEach(point => {
      bboxes.push(new Box(
        point.id,
        point.className,
        point.boundingBox?.x,
        point.boundingBox?.y,
        point.boundingBox?.width,
        point.boundingBox?.height,
        point.isDelete
      ))
    })
    // 深度-高程对的 bbox
    hole.depthElevationPairs?.forEach(pair => {
      bboxes.push(new Box(
        pair.id,
        pair.className,
        pair.boundingBox?.x,
        pair.boundingBox?.y,
        pair.boundingBox?.width,
        pair.boundingBox?.height,
        pair.isDelete
      ))
    })
  })
  return bboxes
}

const buildBboxesFromSectionData = (sectionData): Box[] => {
  const bboxes: Box[] = []
  if (!sectionData) {
    return bboxes
  }
  // 遍历所有剖面信息
  sectionData.sections?.forEach(section => {
    bboxes.push(...buildBboxesFromSectionInfo(section))
  })
  return bboxes
}


/********************* 平面 *********************/

const buildBboxesFromCoordInfo = (coordList): Box[] => {
  const bboxes: Box[] = []
  if (!coordList) {
    return bboxes
  }
  coordList.forEach(coord => {
    // 如果
    if (!coord.isWithLined) {
      bboxes.push(new Box(
        coord.id,
        coord.className,
        coord.boundingBox?.x,
        coord.boundingBox?.y,
        coord.boundingBox?.width,
        coord.boundingBox?.height,
        coord.isDelete
      ))
    }
  })
  return bboxes
}

const buildBboxesFromDrillInfo = (drillList): Box[] => {
  const bboxes: Box[] = []
  if (!drillList) {
    return bboxes
  }
  drillList.forEach(drill => {
    // 钻孔的 bbox
    bboxes.push(new Box(
      drill.id,
      drill.className,
      drill.boundingBox?.x,
      drill.boundingBox?.y,
      drill.boundingBox?.width,
      drill.boundingBox?.height,
      drill.isDelete
    ))
  })
  return bboxes
}

const buildBboxesFromDrillHole = (drillHoleList): Box[] => {
  const bboxes: Box[] = []
  if (!drillHoleList) {
    return bboxes
  }
  drillHoleList.forEach(hole => {
    // 钻孔的 bbox
    bboxes.push(new Box(
      hole.id,
      hole.className,
      hole.boundingBox?.x,
      hole.boundingBox?.y,
      hole.boundingBox?.width,
      hole.boundingBox?.height,
      hole.isDelete
    ))
  })
  return bboxes
}

// 从平面图信息构建边界框
const buildBboxesFromPlaneInfo = (plane): Box[] => {
  const bboxes: Box[] = []
  if (!plane) {
    return bboxes
  }
  bboxes.push(...buildBboxesFromCoordInfo(plane.coordinates))
  bboxes.push(...buildBboxesFromDrillInfo(plane.drillInfos))
  bboxes.push(...buildBboxesFromDrillHole(plane.drills))
  return bboxes
}

/********************* 表格 *********************/

const buildBboxesFromTableInfo = (table): Box[] => {
  const bboxes: Box[] = []
  if (!table) {
    return bboxes
  }
  // 表格
  table.forEach(bbox => {
    if (bbox.className === DetectLabel.TABLE_CELL) {
      return
    }
    bboxes.push(new Box(
      bbox.id,
      bbox.className,
      bbox.boundingBox?.x,
      bbox.boundingBox?.y,
      bbox.boundingBox?.width,
      bbox.boundingBox?.height,
      bbox.isDelete
    ))
  })
  return bboxes
}

/********************* 额外 *********************/

const buildBboxesFromExtraInfo = (extraInfo): Box[] => {
  const bboxes: Box[] = []
  if (!extraInfo) {
    return bboxes
  }
  extraInfo.forEach(item => {
    bboxes.push(new Box(
      item.id,
      item.label,
      item.bbox.x,
      item.bbox.y,
      item.bbox.width,
      item.bbox.height,
      item.isDelete
    ))
  })
  return bboxes
}

/********************* 综合 *********************/

export const transformBboxes = (selectPage) => {
  const bboxes: Box[] = []
  if (!selectPage) {
    return bboxes
  }
  // 剖面图的 bbox
  bboxes.push(...buildBboxesFromSectionData(selectPage.sectionDetectionData))

  // 平面图的 bbox
  bboxes.push(...buildBboxesFromPlaneInfo(selectPage.planeDetectionData))

  // 表格的 bbox
  bboxes.push(...buildBboxesFromTableInfo(selectPage.tableDetectionData?.visionResults))

  // 人工添加的 bbox
  bboxes.push(...buildBboxesFromExtraInfo(selectPage.extraPendingAddBboxes))
  return bboxes
}
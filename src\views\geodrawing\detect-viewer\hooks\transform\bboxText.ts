import { TextItem } from '@/components/DrawingComponents/TextEditor'
import { Box, DetectLabel, VisLabelMap } from '../../types'

const buildBbox = (boundingBox): [number, number, number, number] => {
  return [
    boundingBox?.x,
    boundingBox?.y,
    boundingBox?.x + boundingBox?.width,
    boundingBox?.y + boundingBox?.height
  ]
}

/********************* 剖面 *********************/
// 从剖面信息构建边界框
const buildTextFromSectionInfo = (section): TextItem[] => {
  const bboxes: TextItem[] = []
  if (!section) {
    return bboxes
  }
  // 刻度尺
  const ruler = section.ruler
  if (ruler && ruler.rulerItems && ruler.rulerItems.length > 0) {
    for (let item of ruler.rulerItems) {
      bboxes.push({
        id: item.id,
        bbox: buildBbox(item.boundingBox),
        text: item.originalText || ''
      })
    }
  }
  // 剖面名称本生的 bbox
  const sectionNumber = section.sectionNumber
  if (sectionNumber) {
    bboxes.push({
      id: sectionNumber.id,
      bbox: buildBbox(sectionNumber.boundingBox),
      text: sectionNumber.originalText || ''
    })
  }
  // 钻口
  section.drillHoles?.forEach(hole => {
    // 钻口的 bbox
    bboxes.push({
      id: hole.id,
      bbox: buildBbox(hole.boundingBox),
      text: hole.originalText || ''
    })
    // 深度-高程对的 bbox
    hole.depthElevationPairs?.forEach(pair => {
      bboxes.push({
        id: pair.id,
        bbox: buildBbox(pair.boundingBox),
        text: pair.originalText || ''
      })
    })
    // 钻口间距
    if (hole.assignedSpacing) {
      bboxes.push({
        id: hole.assignedSpacing.id,
        bbox: buildBbox(hole.assignedSpacing.boundingBox),
        text: hole.assignedSpacing.originalText || ''
      })
    }
  })
  return bboxes
}

const buildTextFromSectionData = (sectionData): TextItem[] => {
  const bboxes: TextItem[] = []
  if (!sectionData) {
    return bboxes
  }
  // 遍历所有剖面信息
  sectionData.sections?.forEach(section => {
    bboxes.push(...buildTextFromSectionInfo(section))
  })
  return bboxes
}


/********************* 平面 *********************/

const buildTextFromCoordInfo = (coordList): TextItem[] => {
  const bboxes: TextItem[] = []
  if (!coordList) {
    return bboxes
  }
  coordList.forEach(coord => {
    // 如果
    if (!coord.isWithLined) {
      bboxes.push({
        id: coord.id,
        bbox: buildBbox(coord.boundingBox),
        text: coord.originalText || ''
      })
    }
  })
  return bboxes
}

const buildTextFromDrillInfo = (drillList): TextItem[] => {
  const bboxes: TextItem[] = []
  if (!drillList) {
    return bboxes
  }
  drillList.forEach(drill => {
    // 钻孔的 bbox
    bboxes.push({
        id: drill.id,
        bbox: buildBbox(drill.boundingBox),
        text: drill.originalText || ''
    })
  })
  return bboxes
}

// 从平面图信息构建边界框
const buildTextFromPlaneInfo = (plane): TextItem[] => {
  const bboxes: TextItem[] = []
  if (!plane) {
    return bboxes
  }
  bboxes.push(...buildTextFromCoordInfo(plane.coordinates))
  bboxes.push(...buildTextFromDrillInfo(plane.drillInfos))
  return bboxes
}

/********************* 表格 *********************/

const buildTextFromTableInfo = (table): TextItem[] => {
  const bboxes: TextItem[] = []
  if (!table) {
    return bboxes
  }
  // 表格
  table.forEach(bbox => {
   
  })
  return bboxes
}


/********************* 综合 *********************/

export const transformBboxTexts = (selectPage) => {
  const bboxes: TextItem[] = []
  if (!selectPage) {
    return bboxes
  }
  // 剖面图的 bbox
  bboxes.push(...buildTextFromSectionData(selectPage.sectionDetectionData))

  // 平面图的 bbox
  bboxes.push(...buildTextFromPlaneInfo(selectPage.planeDetectionData))

  // 表格的 bbox
  bboxes.push(...buildTextFromTableInfo(selectPage.tableDetectionData?.visionResults))

  return bboxes
}
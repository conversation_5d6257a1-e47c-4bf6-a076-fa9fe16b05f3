import { DetectLabel, ItemInfo, VisLabelMap } from '../../types';

/******************** 剖面 ********************/
const buildInfoFromSectionInfo = (item, imagePath): ItemInfo[] => {
  const info: ItemInfo[] = []
  const sectionInfo: ItemInfo = {
    id: item.id,
    title: `${item.sectionNumber?.originalText} (剖面)`,
    image: {
      imagePath: imagePath,
      boundingBox: {
        x: item.boundingBox?.x || 0,
        y: item.boundingBox?.y || 0,
        width: item.boundingBox?.width || 0,
        height: item.boundingBox?.height || 0
      }
    },
    expandType: 'list',
    expanded: true,
    children: [] as ItemInfo[],
    '钻孔数量': item.drillHoles?.length || 0,
  }
  // 钻孔信息
  item.drillHoles?.forEach(hole => {
    const holeInfo = {
      id: hole.id,
      title: `${hole.drillName} (钻孔)`,
      image: {
        imagePath: imagePath,
        boundingBox: {
          x: hole.boundingBox?.x || 0,
          y: hole.boundingBox?.y || 0,
          width: hole.boundingBox?.width || 0,
          height: hole.boundingBox?.height || 0
        }
      },
      expandType: 'grid',
      children: [] as ItemInfo[],
      '钻口标高': hole.surfaceElevation || '未知',
      '采样点数量': hole.samples?.length || 0,
    }
    // 采样点信息
    hole.samples?.forEach(point => {
      const title = VisLabelMap[point.className] || VisLabelMap[DetectLabel.SECTION_SOIL_SAMPLE]
      const pointInfo = {
        id: point.id,
        title: `${title} (采样点)`,
        image: {
          imagePath: imagePath,
          boundingBox: {
            x: point.boundingBox?.x || 0,
            y: point.boundingBox?.y || 0,
            width: point.boundingBox?.width || 0,
            height: point.boundingBox?.height || 0
          }
        },
        '深度': point.depth || '未知',
        '高程': point.elevation || '未知',
      }
      holeInfo.children.push(pointInfo)
    })
    sectionInfo.children?.push(holeInfo)
  })
  info.push(sectionInfo)

  return info;
}

const buildInfoFromSectionData = (sectionData, imagePath): ItemInfo[] => {
  const info: ItemInfo[] = [];
  if (!sectionData || !sectionData.sections) {
    return info;
  }

  sectionData.sections.forEach(item => {
    const sectionInfo = buildInfoFromSectionInfo(item, imagePath);
    info.push(...sectionInfo);
  });

  return info;
}

/******************** 平面 ********************/
const buildInfoFromPlaneData = (data, imagePath): ItemInfo[] => {
  const info: ItemInfo[] = [];
  // 坐标系信息
  if (data && data.coordinates) {
    info.push(...buildInfoFromPlaneCoord(data.coordinates, imagePath));
  }
  // 钻口信息及标高信息
  if (data && data.drillInfos) {
    info.push(...buildInfoFromPlaneDrillInfo(data.drillInfos, imagePath));
  }
  // 钻孔
  if (data && data.drills) {
    info.push(...buildInfoFromPlaneHole(data.drills, imagePath));
  }
  return info;
}

const buildInfoFromPlaneCoord = (coordinateList, imagePath): ItemInfo[] => {
  const info: ItemInfo[] = [];
  if (!coordinateList) {
    return info;
  }

  const coordInfos = {
    id: 'plane-coord',
    title: `坐标`,
    expandType: 'grid',
    expanded: true,
    children: [] as ItemInfo[],
  }
  info.push(coordInfos)

  coordinateList.forEach((item, idx) => {
    const planeInfo: ItemInfo = {
      id: item.id || `coord-${idx}`,
      title: `坐标${idx + 1}`,
      image: {
        imagePath: imagePath,
        boundingBox: {
          x: item.boundingBox?.x || 0,
          y: item.boundingBox?.y || 0,
          width: item.boundingBox?.width || 0,
          height: item.boundingBox?.height || 0
        }
      },
      expanded: true,
      'x': item.xCoordinate || '未知',
      'y': item.yCoordinate || '未知',
    }
    coordInfos.children.push(planeInfo)
  })
  return info;
}

const buildInfoFromPlaneDrillInfo = (drillList, imagePath): ItemInfo[] => {
  const info: ItemInfo[] = [];
  if (!drillList) {
    return info;
  }

  const drillInfos = {
    id: 'plane-drill-info',
    title: `钻孔标高`,
    expandType: 'grid',
    expanded: true,
    children: [] as ItemInfo[],
  }
  info.push(drillInfos)

  drillList.forEach((item, idx) => {
    drillInfos.children.push(buildDrillInfo(item, imagePath));
  })
  return info;
}

const buildDrillInfo = (item, imagePath) => {
  const planeInfo: ItemInfo = {
    id: item.id,
    title: `钻孔信息`,
    image: {
      imagePath: imagePath,
      boundingBox: {
        x: item.boundingBox?.x || 0,
        y: item.boundingBox?.y || 0,
        width: item.boundingBox?.width || 0,
        height: item.boundingBox?.height || 0
      }
    },
    '钻孔编号': item.drillNumber || '未知',
    '标高': item.drillElevation || '未知',
    '深度': item.drillDepth || '未知',
    '水位高程': item.waterLevelElevation || '未知',
  }
  return planeInfo;
}

const buildInfoFromPlaneHole = (holeList, imagePath): ItemInfo[] => {
  const info: ItemInfo[] = [];
  if (!holeList) {
    return info;
  }

  const bigHoleInfos = {
    id: 'plane-big-hole',
    title: `大钻孔`,
    expandType: 'grid',
    expanded: true,
    children: [] as ItemInfo[],
  }
  info.push(bigHoleInfos)

  const smallHoleInfos = {
    id: 'plane-small-hole',
    title: `小钻孔`,
    expandType: 'grid',
    expanded: true,
    children: [] as ItemInfo[],
  }
  info.push(smallHoleInfos)

  holeList.forEach((item, idx) => {
    if (item.className === DetectLabel.PLANE_DRILL) {
      const drillInfo = {}
      if (item.bindingInfo) {
        drillInfo['钻口编号'] = item.bindingInfo.drillNumber || '未知';
        drillInfo['钻口深度'] = item.bindingInfo.drillDepth || '未知';
        drillInfo['钻口标高'] = item.bindingInfo.drillElevation || '未知';
        drillInfo['水位高程'] = item.bindingInfo.waterLevelElevation || '未知';
      }
      bigHoleInfos.children.push({
        id: item.id || `big-hole-${idx}`,
        title: `大钻孔${idx + 1}`,
        image: {
          imagePath: imagePath,
          boundingBox: {
            x: item.boundingBox?.x || 0,
            y: item.boundingBox?.y || 0,
            width: item.boundingBox?.width || 0,
            height: item.boundingBox?.height || 0
          },
        },
        expandType: 'grid',
        expanded: true,
        ...drillInfo
      })
    } else if (item.className === DetectLabel.PLANE_LITTLE_DRILL) {
      smallHoleInfos.children.push({
        id: item.id || `small-hole-${idx}`,
        title : `小钻孔${idx + 1}`,
        image: {
          imagePath: imagePath,
          boundingBox: {
            x: item.boundingBox?.x || 0,
            y: item.boundingBox?.y || 0,
            width: item.boundingBox?.width || 0,
            height: item.boundingBox?.height || 0
          },
        },
        '钻孔编号': item.originalText || '未知',
        expandType: 'grid',
        expanded: true,
      })
    } else {
      return; // 跳过未知类型
    }
  })
  return info;
}

/******************** 综合 ********************/
export const transformDetailInfoTree = (selectPage, imagePath): ItemInfo[] => {
  const info: ItemInfo[] = []
  if (!selectPage) {
    return info
  }
  // 获取剖面图信息
  const sectionData = selectPage.sectionDetectionData;
  if (sectionData) {
    info.push(...buildInfoFromSectionData(sectionData, imagePath));
  }
  // 获取平面图信息
  const planeData = selectPage.planeDetectionData;
  if (planeData) {
    info.push(...buildInfoFromPlaneData(planeData, imagePath));
  }
  return info
}
import { AnomalyItem } from '@/components/DrawingComponents/DetectionVisualization'

const buildBbox = (boundingBox): [number, number, number, number] => {
  return [
    boundingBox?.x,
    boundingBox?.y,
    boundingBox?.x + boundingBox?.width,
    boundingBox?.y + boundingBox?.height
  ]
}

/********************* 剖面 *********************/
// 从剖面信息构建边界框
const buildAnomalyFromSectionInfo = (section): AnomalyItem[] => {
  const bboxes: AnomalyItem[] = []
  if (!section) {
    return bboxes
  }

  // 钻口
  section.drillHoles?.forEach(hole => {
    // 钻口的 bbox
    if (hole.difficult) {
      bboxes.push({
        id: hole.id,
        text: hole.drillName,
        anomalyType: hole.difficult.anomalyType || 'anomaly',
        category: hole.className || '',
        confidence: hole.confidence,
        anomalyDetails: hole.difficult.notes || '',
      })
    }
    
    // 深度-高程对的 bbox
    hole.depthElevationPairs?.forEach((pair, index) => {
      if (pair.difficult) {
        bboxes.push({
          id: pair.id,
          text: `${hole.drillName}(高程对${index+1})`,
          anomalyType: pair.difficult.anomalyType || 'anomaly',
          category: pair.className || '',
          confidence: pair.confidence,
          anomalyDetails: pair.difficult.notes || '',
        })
      }
    })
  })
  return bboxes
}

const buildAnomalyFromSectionData = (sectionData): AnomalyItem[] => {
  const bboxes: AnomalyItem[] = []
  if (!sectionData) {
    return bboxes
  }
  // 遍历所有剖面信息
  sectionData.sections?.forEach(section => {
    bboxes.push(...buildAnomalyFromSectionInfo(section))
  })
  return bboxes
}


/********************* 平面 *********************/

// const buildAnomalyFromCoordInfo = (coordList): TextItem[] => {
//   const bboxes: TextItem[] = []
//   if (!coordList) {
//     return bboxes
//   }
//   coordList.forEach(coord => {
//     // 如果
//     if (!coord.isWithLined) {
//       bboxes.push({
//         id: coord.id,
//         bbox: buildBbox(coord.boundingBox),
//         text: coord.originalText || ''
//       })
//     }
//   })
//   return bboxes
// }

// const buildAnomalyFromDrillInfo = (drillList): TextItem[] => {
//   const bboxes: TextItem[] = []
//   if (!drillList) {
//     return bboxes
//   }
//   drillList.forEach(drill => {
//     // 钻孔的 bbox
//     bboxes.push({
//         id: drill.id,
//         bbox: buildBbox(drill.boundingBox),
//         text: drill.originalText || ''
//     })
//   })
//   return bboxes
// }

// // 从平面图信息构建边界框
// const buildAnomalyFromPlaneInfo = (plane): TextItem[] => {
//   const bboxes: TextItem[] = []
//   if (!plane) {
//     return bboxes
//   }
//   bboxes.push(...buildAnomalyFromCoordInfo(plane.coordinates))
//   bboxes.push(...buildAnomalyFromDrillInfo(plane.drillInfos))
//   return bboxes
// }

// /********************* 表格 *********************/

// const buildAnomalyFromTableInfo = (table): TextItem[] => {
//   const bboxes: TextItem[] = []
//   if (!table) {
//     return bboxes
//   }
//   // 表格
//   table.forEach(bbox => {
   
//   })
//   return bboxes
// }


/********************* 综合 *********************/

export const transformAnomalyItem = (selectPage) => {
  const bboxes: AnomalyItem[] = []
  if (!selectPage) {
    return bboxes
  }
  // 剖面图的 bbox
  bboxes.push(...buildAnomalyFromSectionData(selectPage.sectionDetectionData))

  // 平面图的 bbox
  // bboxes.push(...buildAnomalyFromPlaneInfo(selectPage.planeDetectionData))

  // 表格的 bbox
  // bboxes.push(...buildAnomalyFromTableInfo(selectPage.tableDetectionData?.visionResults))

  return bboxes
}
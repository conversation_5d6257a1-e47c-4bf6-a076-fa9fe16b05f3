import { computed, ref, watch } from "vue"
import { transformAnomalyItem } from "./transform/difficult"

export function useAnomalyDetection(pageDataHook, visualizationHook) {

  const { pages, selectedPage, selectedPageIndex } = pageDataHook
  const { addHighlightStates, resetHighlightStates, handleZoomAndLoc } = visualizationHook

  const highlightIds = ref([])
  const curErrIndex = ref(0)

  // 筛选出所有有异常的 page 列表
  const anomalyPages = computed(() => {
    return pages.value
      .map((page, index) => {
        const items = transformAnomalyItem(page)
        return items && items.length > 0
          ? { page, items, index } // 把原始 pageIndex 也带上
          : null
      })
      .filter(Boolean)
  })

  const anomalyPageCnt = computed(() => anomalyPages.value.length)

  // 当前异常对应的 page
  const curAnomalyPage = computed(() => anomalyPages.value[curErrIndex.value] || null)

  // 当前异常页的异常列表
  const curPageAnomalies = computed(() => {
    if (!curAnomalyPage.value) return []

    // 当 selectedPageIndex 与 curErrIndex 对应的 page 不一致时，返回空数组
    if (curAnomalyPage.value.index !== selectedPageIndex.value) {
      return []
    }

    return curAnomalyPage.value.items
  })

  // 同步 selectedPageIndex
  watch(curAnomalyPage, (cur) => {
    if (cur) {
      selectedPageIndex.value = cur.index
    }
  })

  watch(selectedPageIndex, (newIndex) => {
    const idx = anomalyPages.value.findIndex(p => p.index === newIndex)
    if (idx !== -1 && idx !== curErrIndex.value) {
      curErrIndex.value = idx
    }
  })

  const prevDisabled = computed(() => curErrIndex.value <= 0)
  const nextDisabled = computed(() => curErrIndex.value >= anomalyPageCnt.value - 1)

  // 上一个错误页
  const prevError = () => {
    if (!prevDisabled.value) {
      curErrIndex.value--
    }
  }

  // 下一个错误页
  const nextError = () => {
    if (!nextDisabled.value) {
      curErrIndex.value++
    }
  }

  const handleAnomalySelected = (anomaly) => {
    handleZoomAndLoc(anomaly.id, 3, true, false)
  }

  const handleAnomalyHighlighted = (anomalyIds) => {
    highlightIds.value = anomalyIds
    if (anomalyIds && anomalyIds.length > 0) {
      anomalyIds.forEach(id => {
        addHighlightStates(id, false, false)
      })
    } else {
      resetHighlightStates()
    }
  }

  return {
    curErrIndex,
    prevDisabled,
    nextDisabled,
    anomalyPageCnt,
    curPageAnomalies,
    prevError,
    nextError,
    handleAnomalySelected,
    handleAnomalyHighlighted
  }
}
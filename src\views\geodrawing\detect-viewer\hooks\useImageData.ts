import { generateOssPresignedUrl } from "@/api/geodrawing/common"
import { ref } from "vue"


export function useImageData() {

  // 图片URL缓存
  const pageImageUrls = ref({})
  const pageThumbnailUrls = ref({})

  // 懒加载主图
  async function loadPageImageUrl(index, filePath) {
    if (!filePath) return
    if (!pageImageUrls.value[index]) {
      const resp = await generateOssPresignedUrl(filePath)
      pageImageUrls.value[index] = resp.data
    }
  }
  // 懒加载缩略图
  async function loadPageThumbnailUrl(index, filePath) {
    if (!filePath) return
    if (!pageThumbnailUrls.value[index]) {
      const resp = await generateOssPresignedUrl(filePath)
      pageThumbnailUrls.value[index] = resp.data
    }
  }

  return {
    pageImageUrls,
    pageThumbnailUrls,
    loadPageImageUrl,
    loadPageThumbnailUrl,
  }
}
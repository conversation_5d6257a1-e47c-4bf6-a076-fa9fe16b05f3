import { cloneDeep } from 'lodash-es'
import { computed, nextTick, Ref, ref, watch } from "vue"
import { BoundingBox, Box, pageTypeMap } from "../types"
import { transformDetailInfoTree } from "./transform/detailInfo"
import { transformBboxes } from "./transform/bbox"
import { pushToArrayProp, updateAllMatchesByShape, updateAllMatchObjects } from "@/utils/object"

export function usePageData(pages: Ref<Array<any>>, thumbnailsRef, detectionVisRef, imageDataHook) {

  const { pageImageUrls } = imageDataHook || {}

  // 数据备份
  const pagesClone = ref(null)
  // 选中的页面索引
  const selectedPageIndex = ref(0)
  // 选中的页面数据
  const selectedPage = computed(() => pages.value[selectedPageIndex.value] || {})
  // 选中的页面类型
  const selectedPageTypeValue = ref('all')
  // 按钮加载状态
  const btnLoading = ref({})
  // 修复按钮状态
  const btnRepairLoading =ref(false)

  // 监听 pages 变化，跳转缩略图
  watch(selectedPageIndex, async (newIndex) => {
    await nextTick()
    setTimeout(() => {
      scrollToThumbnail(newIndex)
    }, 100) // 延迟一点再滚
  })

  function scrollToThumbnail(newIndex) {
    const thumbnails = thumbnailsRef.value?.querySelectorAll(".thumbnail")
    const targetIndex = showPages.value.findIndex(p => p._originIndex === newIndex)
    if (thumbnails && thumbnails[targetIndex]) {
      thumbnails[targetIndex].scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      })
    }
  }

  // 图纸类型
  const pageTypeEntries = computed(() =>
    Object.entries(pageTypeMap).filter(([key]) => key !== 'all')
  )

  // 展示的页面
  const showPages = computed(() => {
    if (selectedPageTypeValue.value === 'all') {
      // 返回带原始索引的对象
      return pages.value.map((page, idx) => ({ ...page, _originIndex: idx }))
    }
    return pages.value
      .map((page, idx) => ({ ...page, _originIndex: idx }))
      .filter(page => page.pageType === selectedPageTypeValue.value)
  })

  // 下拉框数据
  const pageTypeOptions = computed(() => {
    const options = [{ value: 'all', label: '全部' }]
    if (pages.value.length > 0) {
      const types = new Set(pages.value.map(page => page.pageType))
      // 排序
      const sortedTypes = Array.from(types).sort((a, b) => a - b)
      sortedTypes.forEach(type => {
        options.push({ value: type, label: pageTypeMap[type] || '未知类型' })
      })
    }
    return options
  })

  // 边界框列表
  const boundingBoxes = computed(() => transformBboxes(selectedPage.value))

  // 掩码列表
  const masks = computed(() => {
    const maskList = []
    selectedPage.value.detectionData?.segmentArea?.forEach(segment => {
      if (!segment.points || segment.points.length === 0) return
      // 只处理有点的区域
      maskList.push({
        id: segment.id,
        points: segment.points,
        label: segment.label
      })
    })
    return maskList
  })

  // 检测信息
  const detectionInfo = computed(() => transformDetailInfoTree(selectedPage.value, pageImageUrls.value[selectedPageIndex.value] || ''))

  // 表格数据
  const tableData = computed(() => selectedPage.value.reportConfig || null)

  // 重置页面数据
  const resetPageData = () => {
    pages.value = cloneDeep(pagesClone.value)
  }

  // 新增边界框数据
  const addBboxData = (bboxId, bbox) => {
    pushToArrayProp(
      pages.value[selectedPageIndex.value], 
      'extraPendingAddBboxes',
      {
        id: bboxId,
        label: bbox.label,
        bbox
      }
    )
  }

  // 更新边界框数据
  const updateBboxData = (bboxId, data: BoundingBox) => {
    updateAllMatchesByShape<BoundingBox>(
      pages.value[selectedPageIndex.value], 
      'id', bboxId,
      ['x', 'y', 'width', 'height'], 
      data
    )
  }

  // 删除边界框数据（标记删除）
  const deleteBboxData = (bboxId) => {
    updateAllMatchObjects(
      pages.value[selectedPageIndex.value],
      'id', bboxId,
      { isDelete: true }
    )
  }

  return {
    pages,
    pagesClone,
    selectedPageIndex,
    selectedPage,
    selectedPageTypeValue,
    pageTypeEntries,
    showPages,
    btnLoading,
    btnRepairLoading,
    pageTypeOptions,
    boundingBoxes,
    detectionInfo,
    tableData,
    resetPageData,
    addBboxData,
    updateBboxData,
    deleteBboxData,
  }
}
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {recognizePageWithType, classifyPage } from '@/api/geodrawing/page'
import wsManager from "@/utils/socket"
import { pageTypeMap } from "../types";


export function useRecognizeLoading(fetchFn: Function, pageDataHook) {

  // 重新识别loading状态
  const recognizeLoading = ref({})

  // 选中的页面索引
  const { pages, selectedPageIndex, selectedPage } = pageDataHook

  /**
   * 监听任务进度
   * @param {Array} socketIds - socket ID 数组
   */
  function listenToRecognizeProgress(socketId) {
    if (!socketId) {
      console.warn('socketId 必须是非空字符串');
      return;
    }
      
    wsManager.connect(socketId, `/ws/drawingFileProcess?socketId=${socketId}`, {
      onMessage: (data, socketId) => {
        if (data?.finished) {
          ElMessage.success(`重新识别完成！`);
          const msgData = data?.message ? JSON.parse(data.message) : {};
          recognizeLoading.value[msgData?.page?.id] = false;
          if (fetchFn != null && typeof fetchFn === 'function') {
            fetchFn();
          }
          wsManager.disconnect(socketId);
          return;
        }
      },
      onError: (error, socketId) => {
        ElMessage.error(`WebSocket 连接错误（socketId=${socketId}）: ${error.message || '未知错误'}`);
      }
    });
  }

  // 重新识别页面
  const reRecognizePage = async (pageId, type) => {
    if (type) {
      const classifyResp = await classifyPage(pageId)
      if (classifyResp.code !== 200) {
        ElMessage.error(classifyResp.msg || 'AI判断类别失败，请稍后重试。')
        return
      }
      if (classifyResp.data !== type) {
        // 要求用户确实是否使用指定类别来识别
        const confirm = await ElMessageBox.confirm(`当前页面AI判断为 "${pageTypeMap[classifyResp.data]}"，是否强制使用 "${pageTypeMap[type]}" 进行识别？`)
        if (!confirm) return
      }
    }
    
    const resp = await recognizePageWithType(pageId, type)
    if (resp.code === 200) {
      listenToRecognizeProgress(resp.data)
    } else {
      ElMessage.error(resp.msg || '重新识别失败，请稍后重试。')
    }
  }

  const handleReRecognize = async (type) => {
    recognizeLoading.value[selectedPage.value.id] = true
    try {
      // 调用后端重新识别接口
      const pageId = selectedPage.value.id || pages.value[selectedPageIndex.value]?.id
      if (!pageId) {
        ElMessage.error('请选择要重新识别的页面。')
        return
      }
      await reRecognizePage(pageId, type)
      ElMessage.success('重新识别请求已提交，请稍后。')
    } catch (error) {
      console.error('重新识别失败:', error)
      ElMessage.error('重新识别失败，请稍后重试。')
    }
  }

  return {
    recognizeLoading,
    handleReRecognize,
  }
}
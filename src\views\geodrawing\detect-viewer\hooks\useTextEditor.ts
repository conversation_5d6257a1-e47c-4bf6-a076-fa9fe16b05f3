import { TextItem } from "@/components/DrawingComponents/TextEditor"
import { computed, ref, watch } from "vue"
import { transformBboxTexts } from "./transform/bboxText"

interface ChangeLog {
  id: string | number
  oldText: string
  newText: string
  timestamp: string
}

export const useTextEditor = (pageDataHook) => {

  const { selectedPageIndex, selectedPage } = pageDataHook

  const changeLog = ref<ChangeLog[]>([])

  const textItems = ref<TextItem[]>([])

  watch(selectedPage, (newPage) => {
    textItems.value = transformBboxTexts(newPage)
  }, { immediate: true, deep: true })

  const canvasWidth = computed(() => {
    return selectedPage.value?.sectionDetectionData?.imageWidth ||
      selectedPage.value?.planeDetectionData?.imageWidth ||
      selectedPage.value?.tableDetectionData?.imageWidth ||
      1000
  })

  const canvasHeight = computed(() => {
    return selectedPage.value?.sectionDetectionData?.imageHeight ||
      selectedPage.value?.planeDetectionData?.imageHeight ||
      selectedPage.value?.tableDetectionData?.imageHeight ||
      700
  })

  // 处理文本项更新
  const handleTextItemsUpdate = (updatedTextItems: TextItem[]) => {
    textItems.value = updatedTextItems
    console.log('文本项已更新:', updatedTextItems)
  }

  // 处理单个文本变化
  const handleTextChanged = (item: TextItem, oldText: string, newText: string) => {
    console.log(`文本项 ${item.id} 已修改:`, { item, oldText, newText })
  }

  // 处理项目更新（用于后端同步）
  const handleItemUpdated = (id: string | number, newText: string, oldText: string) => {
    // 记录修改日志
    changeLog.value.unshift({
      id,
      oldText,
      newText,
      timestamp: new Date().toLocaleString()
    })
    
    // 这里可以调用API发送给后端
    console.log('需要同步到后端的数据:', {
      id,
      text: newText
    })
    
    // 示例：发送给后端
    // updateTextItemToBackend(id, newText)
  }

  return {
    textItems,
    canvasWidth,
    canvasHeight,
    changeLog,
    handleTextItemsUpdate,
    handleTextChanged,
    handleItemUpdated,
  }
}
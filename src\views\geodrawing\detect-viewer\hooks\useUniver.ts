import { computed, ref, watch } from "vue"
import { buildNestedSheetConfig } from "../utils/convertor"
import { PageType } from "../types"

export function useUniver(pageDataHook) {

  const { detectionInfo, selectedPageTypeValue, tableData: rawTableData } = pageDataHook

  // 使用 ref 来缓存 tableData，避免每次都创建新对象
  const tableData = ref(null)

  // 监听 detectionInfo 变化，更新 tableData
  watch(() => detectionInfo, (newDetectionInfo) => {
    if (newDetectionInfo && selectedPageTypeValue.value != PageType.MISALIGNED_TABLE) {
      tableData.value = buildNestedSheetConfig(newDetectionInfo.value, '数据报表', 4)
    }
  }, { deep: true })

  watch(() => rawTableData.value, (newTableData) => {
    if (newTableData && selectedPageTypeValue.value == PageType.MISALIGNED_TABLE) {
      tableData.value = newTableData.sheets
    }
  })

  const onTableReady = () => {}
  const onDataChange = () => {}

  return {
    tableData,
    onTableReady,
    onDataChange
  }
}
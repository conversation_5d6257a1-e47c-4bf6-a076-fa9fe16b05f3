import { ElMessage } from "element-plus"
import { computed, Ref, ref } from "vue"
import { submitLeaders } from '@/api/geodrawing/page'
import { DetectLabel, getVisualLabelGroup, PageTypePrefixMap } from "../types"
import { repairLogicVisionResult } from '@/api/geodrawing/file'
import { updateVisionResult, recomputeLogicResult } from '@/api/geodrawing/page'


export function useVisualization(
  fetchFn: Function, 
  accordionRef: Ref, detectionVisRef: Ref, textEditorRef: Ref,
  pageDataHook
) {

  const { 
    selectedPage, 
    boundingBoxes, 
    addBboxData, 
    updateBboxData, 
    deleteBboxData, 
    resetPageData, 
    btnLoading, 
    btnRepairLoading 
  } = pageDataHook

  // 高亮状态
  const highlightStates = ref([])
  
  // bbox控制相关
  const showBboxLabels = ref(true)
  const bboxStrokeWidth = ref(2)
  
  // 元素相关
  const visibleCategories = ref([])
  const showAllCategories = ref(true)
  
  // 标记当前页面的引线数据是否有未提交的修改
  const hasUnsavedLeaderChanges = ref(false)
  // 添加页引线数据
  const currentPageLeaders = ref([])

  // 鼠标位置信息
  const mousePosition = ref(null)
  const handleMouseMove = (event) => {
    mousePosition.value = event
  }

  // 所有类别
  const allCategories = computed(() => {
    return getVisualLabelGroup(PageTypePrefixMap[selectedPage.value.pageType])
  })

  const highlightIds = computed(() => {
    return highlightStates.value.map(state => state.key)
  })

  const highlightAnimationKeys = computed(() => {
    return highlightStates.value.filter(state => state.animation).map(state => state.key)
  })

  // 引线数据
  const leaders = computed(() => transformLeaderData(selectedPage.value, boundingBoxes.value))

  function transformLeaderData(page, bboxesList) {
    const resultList = []
    if (
      page.planeDetectionData &&
      Array.isArray(page.planeDetectionData.coordinates)
    ) {
      page.planeDetectionData.coordinates.forEach(coordinate => {
        const point = coordinate.point
        if (point && point.x !== 0 && point.y !== 0 && coordinate.id) {
          if (detectionVisRef.value?.buildLeaderData) {
            const leaderItem = detectionVisRef.value.buildLeaderData(point, coordinate.id, bboxesList)
            if (leaderItem) {
              resultList.push(leaderItem)
            }
          }
        }
      })
    }
    return resultList
  }

  // 切换显示类别
  const handleCategoryChanged = (data) => {
    switch (data.action) {
      case 'show-all':
        showAllCategories.value = true
        break
      case 'toggle':
        if (showAllCategories.value) {
          showAllCategories.value = false
          visibleCategories.value = [data.category]
        } else {
          toggleCategoryVisibility(data.category)
        }
        break
    }
  }

  // 切换类别可见性
  const toggleCategoryVisibility = (category) => {
    if (showAllCategories.value) return
    
    const index = visibleCategories.value.indexOf(category)
    if (index > -1) {
      visibleCategories.value.splice(index, 1)
    } else {
      visibleCategories.value.push(category)
    }
    if (visibleCategories.value.length === 0) {
      showAllCategories.value = true
    }
  }

  const resetBboxSelection = () => {
    if (detectionVisRef.value) {
      detectionVisRef.value.resetBboxSelection()
    }
  }

  // 提交引线数据
  const submitLeader = () => {
    const currentLeaders = currentPageLeaders.value || []
    // 校验数据
    if (currentLeaders.length === 0) {
      ElMessage.warning('没有引线数据可提交')
      return
    }
    if (currentLeaders.length > 3) {
      ElMessage.warning('每页最多只能提交3条引线数据')
      return
    }
    // 构建数据
    const data = []
    const pageId = selectedPage.value.id
    currentLeaders.forEach(leader => {
      data.push({
        pageId,
        point: leader.targetPoint,
        bboxId: boundingBoxes.value[leader.bboxIndex]?.id,
      })
    })
    // 提交数据
    btnLoading.value[pageId] = true
    submitLeaders(data).then(resp => {
      if (resp.code === 200) {
        ElMessage.success('引线数据提交成功')
        hasUnsavedLeaderChanges.value = false
        fetchFn()
      } else {
        ElMessage.error(resp.msg || '引线数据提交失败，请稍后重试')
      }
      btnLoading.value[pageId] = false
    }).catch(err => {
      btnLoading.value[pageId] = false
      console.error('提交引线数据失败:', err)
      ElMessage.error('引线数据提交失败，请稍后重试')
    })
  }

  // 导航到指定项目
  const navigateTo = async (targetKey) => {
    if (!targetKey.trim()) {
      alert('请输入有效的项目ID')
      return
    }
    
    if (accordionRef.value) {
      const success = await accordionRef.value.navigateToItem(targetKey)
      if (!success) {
        // ElMessage.error(`未找到ID为 "${targetKey}" 的项目`)
      }
    }
  }

  const addHighlightStates = (bboxId: string, animation: boolean, autoclear: boolean = true) => {
    const existingState = highlightStates.value.find(state => state.key === bboxId)
    if (existingState) {
      existingState.animation = animation
    } else {
      highlightStates.value.push({ type: 'bbox', key: bboxId, animation })
    }
    setTimeout(() => {
      if (autoclear) {
        resetHighlightStates([bboxId])
      } else {
        highlightStates.value.find(state => state.key === bboxId).animation = false
      }
    }, 1000)
  }

  const resetHighlightStates = (ids = []) => {
    if (ids && ids.length > 0) {
      highlightStates.value = highlightStates.value.filter(state => !ids.includes(state.key))
    } else {
      highlightStates.value = []
    }
  }

  const handleRecomputeLogicResult = () => {
    const visionResultDto = {
      drawingPageId: selectedPage.value.id,
    };

    // 调用后端接口
    const pageId = selectedPage.value.id
    btnLoading.value[pageId] = true;
    recomputeLogicResult(visionResultDto).then(response => {
      if (response.code === 200) {
        ElMessage.success('重计算成功')
        fetchFn()
        resetBboxSelection()
      } else {
        ElMessage.error(response.msg || '重计算失败')
      }
      btnLoading.value[pageId] = false;
    }).catch(error => {
      btnLoading.value[pageId] = false;
      console.error('重计算失败:', error)
      ElMessage.error('重计算失败，请稍后重试')
    });
  }

  const handleRepairVisionResult = (drawingId) => {
    btnRepairLoading.value = true
    repairLogicVisionResult(drawingId).then(resp => {
      if (resp.code === 200) {
        ElMessage.success('数据修复成功')
        fetchFn()
        resetBboxSelection()
      }else {
        ElMessage.error(resp.msg || '数据修复失败')
      }
      btnRepairLoading.value = false;
    }).catch(error => {
      btnRepairLoading.value = false;
      console.error('数据修复失败:', error)
      ElMessage.error('数据修复失败，请稍后重试')
    });
  }

  const handleBboxUpdated = (data) => {
    const visionResultDto = {
      drawingPageId: selectedPage.value.id,
      changes: data.changes?.map(change => ({
        id: change.index,
        className: change.bbox.label,
        bbox: {
          x: change.bbox.x,
          y: change.bbox.y,
          width: change.bbox.width,
          height: change.bbox.height
        },
        changeType: change.changeType, // "resize", "add", "drag", "delete"
        timestamp: change.timestamp
      }))
    };

    // 调用后端接口
    const pageId = selectedPage.value.id
    btnLoading.value[pageId] = true
    updateVisionResult(visionResultDto).then(response => {
      if (response.code === 200) {
        ElMessage.success('更新成功')
        fetchFn()
        resetBboxSelection()
      } else {
        ElMessage.error(response.msg || '更新失败')
      }
      btnLoading.value[pageId] = false
    }).catch(error => {
      btnLoading.value[pageId] = false
      console.error('更新失败:', error)
      ElMessage.error('更新失败，请稍后重试')
    });
  }

  const handleBboxTempUpdated = (data) => {
    if (!data || !data.bbox) {
      ElMessage.error('边界框数据不完整')
      return
    }

    if (data.type === 'delete') {
      deleteBboxData(data.bbox.id)
    } else if (data.type === 'add') {
      addBboxData(data.bbox.id, data.bbox)
    } else {
      updateBboxData(data.bbox.id, data.bbox)
    }
  }

  const handleBboxTempCancel = () => {
    resetPageData()
  }

  // 处理项目点击事件
  const handleItemClick = (item) => {
    addHighlightStates(item.id, true)
  }

  // 处理引线添加事件
  const handleLeaderAdded = (leader) => {
    currentPageLeaders.value.push(leader)
    hasUnsavedLeaderChanges.value = true
  }

  // 处理引线移除事件
  const handleLeaderRemoved = (data) => {
    if (data === 'all') {
      currentPageLeaders.value = []
    } else {
      currentPageLeaders.value.splice(data.index, 1)
    }
    hasUnsavedLeaderChanges.value = true
  }

  // 缩放到指定位置
  const handleZoomAndLoc = (id, zoom, animation = false, autoclear = true) => {
    if (detectionVisRef.value) {
      // 找到对应 bbox
      const item = detectionVisRef.value.getElemById(id, 'bbox')
      const bbox = item?.originalBbox
      const point = {
        x: bbox.x + bbox.width / 2,
        y: bbox.y + bbox.height / 2
      }

      // 特殊类型放大比例调整
      if (DetectLabel.SECTION === item.label) {
        zoom = 1.15
      }
      
      detectionVisRef.value.zoomToPoint(point, zoom)
      addHighlightStates(id, animation, autoclear)
    }
  }

  const handleBboxClick = (bbox) => {
    if (bbox.bbox?.id) {
      navigateTo(bbox.bbox.id)

      textEditorRef.value?.startEditById(bbox.bbox.id, { 
        zoomScale: 1.2,
        padding: 100,
        animationDuration: 300
      })
    }
  }

  function setBboxLabelVisible(visible) {
    showBboxLabels.value = !!visible
  }

  function setBboxStrokeWidth(width) {
    const n = Number(width)
    if (!isNaN(n) && n > 0) {
      bboxStrokeWidth.value = n
    }
  }

  return {
    leaders,
    showBboxLabels,
    bboxStrokeWidth,
    hasUnsavedLeaderChanges,
    currentPageLeaders,
    visibleCategories,
    showAllCategories,
    allCategories,
    highlightIds,
    highlightAnimationKeys,
    mousePosition,

    handleCategoryChanged,
    toggleCategoryVisibility,
    submitLeader,
    navigateTo,
    handleItemClick,
    handleLeaderAdded,
    handleLeaderRemoved,
    handleBboxUpdated,
    handleBboxTempUpdated,
    handleBboxTempCancel,
    handleZoomAndLoc,
    handleBboxClick,
    handleMouseMove,
    handleRecomputeLogicResult,
    handleRepairVisionResult,
    addHighlightStates,
    resetHighlightStates,
    setBboxLabelVisible,
    setBboxStrokeWidth
  }
}
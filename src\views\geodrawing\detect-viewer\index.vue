<template>
  <div class="pdf-viewer-container">
    <!-- 顶部图纸信息 -->
    <div class="header">
      <div class="file-name">{{ fileInfo.fileName }}</div>
      <div class="file-desc">
        <span>类型：{{ fileInfo.fileType === 0 ? '总图' : '勘察报告' }}</span> ｜ 
        <span>状态：
          <el-tag
            :type="statusClassMap[fileInfo.status] || 'info'"
            size="small"
          >{{ statusMap[fileInfo?.status] }}</el-tag>
        </span>
        <span>
          <el-dropdown
            class="re-recognize-dropdown"
            :disabled="recognizeLoading[selectedPageIndex]"
            split-button
            type="primary"
            @click="handleReRecognize(null)"
            @command="handleReRecognize"
          >
            重新识别
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="[key, value] in pageTypeEntries"
                  :key="key"
                  :command="key"
                >
                  {{ value }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="warning" :loading="btnLoading[selectedPage.id]" @click="handleRecomputeLogicResult">重计算</el-button>
          <el-button type="success" :loading="btnRepairLoading" @click="handleRepairVisionResult(route.params && route.params.drawingId)">数据修复</el-button>
        </span>
        <!-- 异常指示器-->
        <div v-if="anomalyPageCnt > 0" class="floating-indicator">
          <div class="indicator-content">
            <span class="indicator-icon">⚠️</span>
            <span class="indicator-text">{{ anomalyPageCnt }} 个异常</span>
          </div>
          <div class="indicator-operator">
            <el-button
              :disabled="prevDisabled"
              @click="prevError"
              circle
              size="small"
              :icon="ArrowLeft"
            />
            <el-button
              :disabled="nextDisabled"
              @click="nextError"
              circle
              size="small"
              :icon="ArrowRight"
            />
          </div>
        </div>
        <!-- 加载中指示器 -->
        <span>
          <div class="loader loading" v-if="recognizeLoading[selectedPage.id]"></div>
        </span>
      </div>
    </div>

    <div class="content">
      <el-splitter>
        <el-splitter-panel :size="150" :min="150" :max="200">
          <!-- 左侧缩略图导航 -->
          <div class="thumbnails-container">
            <div class="thumbnail-dropdown">
              <el-select v-model="selectedPageTypeValue">
                <el-option
                  v-for="item in pageTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div class="thumbnails" ref="thumbnailsRef">
              <div
                v-for="(page, index) in showPages"
                :key="page._originIndex"
                :class="['thumbnail', selectedPageIndex === page._originIndex ? 'active' : '']"
                @click="selectPage(page._originIndex)"
              >
                <img
                  :src="pageThumbnailUrls[page._originIndex] || '/images/loading.gif'"
                  v-lazy="() => loadPageThumbnailUrl(page._originIndex, page.thumbnailPath)"
                />
                <p>第 {{ page.pageNumber }} 页</p>
              </div>
            </div>
          </div>
        </el-splitter-panel>
        <el-splitter-panel>
          <!-- 中间主图 -->
          <div class="main-image">
            <DetectionVisualization
              ref="detectionVisRef"
              v-if="selectedPage.imagePath && pageImageUrls[selectedPageIndex]"
              :imageSrc="pageImageUrls[selectedPageIndex]"
              :max-zoom="10"
              :bbox-list="boundingBoxes"
              :leaders="currentPageLeaders"
              :max-total-leaders="maxLeaderCount"
              :max-leaders-per-bbox="1"
              :enable-zoom="true"
              :enable-pan="true"
              :enable-bbox-edit="true"
              :highlight-keys="highlightIds"
              :highlight-animation-keys="highlightAnimationKeys"
              :show-bbox-label="showBboxLabels"
              :bbox-stroke-width="bboxStrokeWidth"
              :visible-categories="visibleCategories"
              :show-all-categories="showAllCategories"
              :all-categories="allCategories"
              :category-map="VisLabelMap"
              :anomaly-results="curPageAnomalies"
              @leader-added="handleLeaderAdded"
              @leader-removed="handleLeaderRemoved"
              @bbox-click="handleBboxClick"
              @bbox-updated="handleBboxUpdated"
              @bbox-temp-updated="handleBboxTempUpdated"
              @bbox-temp-cancel="handleBboxTempCancel"
              @category-changed="handleCategoryChanged"
              @mouse-move="handleMouseMove"
              @anomaly-selected="handleAnomalySelected"
              @anomaly-highlighted="handleAnomalyHighlighted"
            >
              <template #leader-controls="{ isLeaderEditMode, isBboxEditMode, changeStats, saveBboxChanges, cancelBboxChanges }">
                <div class="leader-controls">
                  <el-button
                    type="info"
                    :loading="btnLoading[selectedPage.id]"
                    v-if="isLeaderEditMode && hasUnsavedLeaderChanges && currentPageLeaders?.length >= maxLeaderCount" 
                    @click="submitLeader"
                  >提交引线</el-button>
                  <el-button
                    type="primary"
                    v-if="isBboxEditMode"
                    :disabled="!changeStats.hasChanges"
                    @click="cancelBboxChanges"
                  >取消修改</el-button>
                  <el-button
                    type="success"
                    v-if="isBboxEditMode"
                    :loading="btnLoading[selectedPage.id]"
                    :disabled="!changeStats.hasChanges"
                    @click="saveBboxChanges"
                  >保存修改</el-button>
                </div>
              </template>
            </DetectionVisualization>
          </div>
        </el-splitter-panel>
        <el-splitter-panel :size="'30%'">
          <!-- 右侧检测结果区域 -->
          <div class="detection-result">
            <h3>检测结果 - 第 {{ selectedPage.pageNumber }} 页 {{ `(检测类别：${pageTypeMap[selectedPage.pageType] || '未知类型'})` }}</h3>
            <el-tabs type="border-card">
              <el-tab-pane label="表格统计">
                <UniverTable
                  :sheet-config="tableData"
                  @ready="onTableReady"
                  @change="onDataChange"
                />
              </el-tab-pane>
              <el-tab-pane label="可视化">
                <NestedAccordion
                  ref="accordionRef"
                  :data="detectionInfo"
                  :config="accordionConfig"
                  :grid-threshold="3"
                  :grid-columns="3"
                  :margin="100"
                  @item-click="handleItemClick"
                  @item-detail-click="handleItemClick"
                >
                  <template #image-tools="{ item, index, mode, imageType } = {}">
                    <el-icon @click.stop="handleZoomAndLoc(item.id)">
                      <el-tooltip content="定位" placement="top">
                        <Aim />
                      </el-tooltip>
                    </el-icon>
                  </template>
                </NestedAccordion>
              </el-tab-pane>
              <el-tab-pane label="文本编辑">
                <TextEditor
                  v-if="selectedPage.imagePath && pageImageUrls[selectedPageIndex]"
                  :drawing-page-id="selectedPage.id"
                  :text-items="textItems"
                  :canvas-width="canvasWidth"
                  :canvas-height="canvasHeight"
                  :background-image="pageImageUrls[selectedPageIndex]"
                  :background-opacity="0.8"
                  text-box-background-color="rgba(255,0,0,0.5)"
                  :show-control-panel="true"
                  :show-zoom-info="true"
                  :show-canvas-info="false"
                  :enable-debug-buttons="false"
                  :min-scale="0.1"
                  :max-scale="3"
                  :scale-step="0.1"
                  @text-changed="handleTextChanged"
                  @text-updated="handleTextUpdated"
                  ref="textEditorRef"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-splitter-panel>
      </el-splitter>

    </div>

    <!-- 底部工具栏 -->
    <div class="footer">
      <div class="footer-controls">

        <!-- 靠左信息 -->
        <div class="control-group mouse-info-group">
          <el-tooltip content="鼠标在图像上的坐标信息" placement="top">
            <el-icon><i class="el-icon-info" /></el-icon>
          </el-tooltip>
          <span class="mouse-label">容器:</span>
          <span class="mouse-value">{{ mousePosition?.containerCoords?.x?.toFixed(0) ?? '-' }},{{ mousePosition?.containerCoords?.y?.toFixed(0) ?? '-' }}</span>
          <span class="mouse-label">图像:</span>
          <span class="mouse-value">{{ mousePosition?.imageCoords?.x?.toFixed(0) ?? '-' }},{{ mousePosition?.imageCoords?.y?.toFixed(0) ?? '-' }}</span>
          <span class="mouse-label">归一:</span>
          <span class="mouse-value">{{ mousePosition?.normalizedCoords?.x?.toFixed(2) ?? '-' }},{{ mousePosition?.normalizedCoords?.y?.toFixed(2) ?? '-' }}</span>
          <span class="mouse-label">在图像内:</span>
          <span class="mouse-value">{{ mousePosition?.isInImage ? '是' : '否' }}</span>
        </div>

        <!-- 靠右工具按钮 -->
        <div class="control-group other-controls">
        
          <div class="control-group">
            <el-checkbox 
              v-model="showBboxLabels" 
              @change="setBboxLabelVisible"
              size="small"
            >
              显示检测框标签
            </el-checkbox>
          </div>
          
          <div class="control-group">
            <el-popover
              placement="top"
              :width="60"
              trigger="click"
              popper-class="bbox-width-popover"
            >
              <template #reference>
                <el-button size="small" type="primary" plain>
                  边框粗细: {{ bboxStrokeWidth }}
                </el-button>
              </template>
              <div class="slider-container">
                <el-slider
                  v-model="bboxStrokeWidth"
                  :min="1"
                  :max="10"
                  :step="1"
                  vertical
                  height="120px"
                  @change="setBboxStrokeWidth"
                  show-tooltip
                  :format-tooltip="(val) => `${val}px`"
                />
              </div>
            </el-popover>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue"
import { UniverTable } from "@/components/UniverTable"
import { cloneDeep } from 'lodash-es'
import { usePageData, useImageData, useRecognizeLoading, useVisualization, useUniver, useTextEditor, useAnomalyDetection } from "./hooks"
import { queryDetectionResult } from '@/api/geodrawing/file'
import { statusClassMap, statusMap, pageTypeMap, VisLabelMap } from './types'
import DetectionVisualization from "@/components/DrawingComponents/DetectionVisualization"
import NestedAccordion from "@/components/DrawingComponents/NestedAccordion/index.vue"
import TextEditor from "@/components/DrawingComponents/TextEditor"

const { proxy } = getCurrentInstance()

const route = useRoute()

const maxLeaderCount = 2
const accordionRef = ref(null)
const detectionVisRef = ref(null)
const textEditorRef = ref(null)
const thumbnailsRef = ref(null)

// 图纸文件信息
const fileInfo = ref({
  fileName: '建筑结构图纸A',
  fileType: '结构图',
  status: 0,
})

// 组件配置
const accordionConfig = {
  titleField: 'title',
  imageField: 'image',
  descriptionField: 'description',
  childrenField: 'children',
  keyField: 'id',
  gridDescriptionField: 'description',
  excludeFields: ['children', 'id', 'expandType', 'expanded'],
}

// 选中的页面索引
const pages = ref([])

// 页面图片管理
const imageDataHook = useImageData(), { 
  pageImageUrls, pageThumbnailUrls, loadPageImageUrl, loadPageThumbnailUrl 
} = imageDataHook

// 页面数据管理
const pageDataHook = usePageData(pages, thumbnailsRef, detectionVisRef, imageDataHook), {
  btnLoading,
  btnRepairLoading,
  pagesClone,
  selectedPageIndex,
  selectedPage,
  selectedPageTypeValue,
  pageTypeEntries,
  showPages,
  pageTypeOptions,
  boundingBoxes,
  detectionInfo,
} = pageDataHook

// 重新识别 loading 管理
const { recognizeLoading, handleReRecognize } = useRecognizeLoading(fetchPages, pageDataHook)

// 视觉组件相关
const visualizationHook = useVisualization(fetchPages, accordionRef, detectionVisRef, textEditorRef, pageDataHook), {
  leaders,
  showBboxLabels,
  bboxStrokeWidth,
  hasUnsavedLeaderChanges,
  currentPageLeaders,
  visibleCategories,
  showAllCategories,
  allCategories,
  highlightIds,
  highlightAnimationKeys,
  mousePosition,
  handleCategoryChanged,
  submitLeader,
  handleItemClick,
  handleLeaderAdded,
  handleLeaderRemoved,
  handleZoomAndLoc,
  handleBboxTempUpdated,
  handleBboxUpdated,
  handleBboxTempCancel,
  handleBboxClick,
  handleRecomputeLogicResult,
  handleRepairVisionResult,
  setBboxLabelVisible,
  setBboxStrokeWidth,
  handleMouseMove
} = visualizationHook

// 报表组件
const univerHook = useUniver(pageDataHook), {
  tableData,
  onTableReady,
  onDataChange
} = univerHook

// 文本编辑
const textEditorHook = useTextEditor(pageDataHook), {
  textItems,
  canvasWidth,
  canvasHeight,
  changeLog,
  handleTextItemsUpdate,
  handleTextChanged,
  handleItemUpdated,
} = textEditorHook

// 异常检测
const anomalyDetectionHook = useAnomalyDetection(pageDataHook, visualizationHook), {
  prevDisabled,
  nextDisabled,
  anomalyPageCnt,
  curPageAnomalies,
  prevError,
  nextError,
  handleAnomalySelected,
  handleAnomalyHighlighted
} = anomalyDetectionHook

// 处理文本更新完成事件
function handleTextUpdated(data) {
  console.log('文本更新完成:', data);
  // 可以在这里添加成功提示或其他处理逻辑
  if (data.totalChanges > 0) {
    proxy.$modal.msgSuccess(`成功保存 ${data.totalChanges} 个文本修改`);
  }
}
// 选择页面
async function selectPage(index) {
  // 如果点击的是当前页面，直接返回
  if (index === selectedPageIndex.value) return
  
  // 检查是否有未提交的修改
  if (hasUnsavedLeaderChanges.value) {
    try {
      await proxy.$modal.confirm('当前页面有未提交的引线修改，切换页面将丢失这些修改。是否继续？')
      // 用户确认切换，重置修改标记并切换页面
      hasUnsavedLeaderChanges.value = false
      selectedPageIndex.value = index
    } catch {
      // 用户取消切换，不做任何操作
      return
    }
  } else {
    // 没有未提交的修改，直接切换
    selectedPageIndex.value = index
  }
}

// 监听页面切换
watch(selectedPageIndex, async (newIndex, oldIndex) => {
  currentPageLeaders.value = leaders.value || []
  loadPageImageUrl(newIndex, pages.value[newIndex]?.imagePath)
}, { immediate: true })

// 页面加载完成后获取数据
onMounted(async () => {
  await fetchPages()
  if (pages.value.length > 0) {
    loadPageImageUrl(0, pages.value[0].imagePath)
    loadPageThumbnailUrl(0, pages.value[0].thumbnailPath)
  }
})

// 获取页面数据
async function fetchPages() {
  if (route.params && route.params.drawingId) {
    const resp = await queryDetectionResult(route.params.drawingId)
    fileInfo.value = resp.data.drawingFile
    pages.value = resp.data.drawingPages
    pagesClone.value = cloneDeep(pages.value)
    // 预加载所有缩略图
    if (pages.value.length > 0) {
      loadPageThumbnailUrl(0, pages.value[0].thumbnailPath)
    }
  }
}
</script>

<style scoped lang="scss">
.pdf-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 800px;
}

.header {
  display: flex;
  flex-direction: row;
  background-color: #f2f2f2;
  border-bottom: 1px solid #ddd;
  padding: 0 8px;
  height: 60px;
  line-height: 60px;
}

.header .file-name {
  font-size: 1.2em;
  font-weight: bold;
  margin-right: 20px;
} 

.header .file-desc {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9em;
  color: #666;


  // 异常指示器
  .floating-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    line-height: normal !important;

    .indicator-content {
      background: rgba(220, 53, 69, 0.9);
      color: white;
      padding: 6px 12px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      
      &:hover {
        background: rgba(220, 53, 69, 1);
      }
    }

    .indicator-icon {
      font-size: 14px;
      margin-right: 6px;
    }
  }
}

.content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.thumbnails-container {
  height: 100%;
  display: flex;
  position: relative;
  flex-direction: column;
  border-right: 1px solid #ddd;
}

.thumbnail-dropdown {
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 2;
  padding: 8px 10px;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.thumbnails {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px 10px 10px;
}

.thumbnail {
  cursor: pointer;
  margin-bottom: 10px;
  text-align: center;
  box-sizing: border-box;
  border: 2px solid #f0f0f0;
  margin-top: 10px;
}

.thumbnail:first-of-type {
  margin-top: 10px;
}

.thumbnail img {
  width: 100%;
  height: auto;
}

.thumbnail.active {
  border: 2px solid #409eff;
}

.main-image {
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.detection-result {
  padding: 10px;
  overflow-y: auto;
  height: 100%;
}

.detection-result :deep(.el-tabs) {
  min-height: calc(100% - 4rem);
}

.univer-table-wrapper {
  height: calc(100vh - 16.5rem);
}

.footer {
  padding: 8px;
  border-top: 1px solid #ddd;
  text-align: right;
  background: #f9f9f9;
}

.footer-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  background: #f8f9fa;
  color: #555;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mouse-info-group {
  gap: 6px;
}

.mouse-label {
  font-weight: 500;
  color: #666;
}

.mouse-value {
  font-family: monospace;
  color: #222;
  padding: 2px 6px;
  background: #f1f3f4;
  border-radius: 4px;
}

.slider-container {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.re-recognize-dropdown {
  vertical-align: middle;
  margin: 0 10px;
}
.loading {
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
}
.loader {
  border: 4px solid #f3f3f3; /* Light grey */
  border-top: 4px solid #3498db; /* Blue */
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 全局样式 - 弹出式滑块美化 */
:deep(.bbox-width-popover) {
  padding: 8px;
}

:deep(.bbox-width-popover .el-slider) {
  margin: 0;
}

:deep(.bbox-width-popover .el-slider__runway) {
  margin: 0;
}

:deep(.bbox-width-popover .el-slider__bar) {
  background-color: #409eff;
}

:deep(.bbox-width-popover .el-slider__button) {
  border-color: #409eff;
}
</style>

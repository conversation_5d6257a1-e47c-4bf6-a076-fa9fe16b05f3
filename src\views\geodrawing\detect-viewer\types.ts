import { groupByPrefixes } from '@/utils/object'


export enum PageType { 

  OTHER_FORM = "0",
  SECTION_VIEW = "1",
  LEGEND = "2",
  RICH_TEXT = "3",
  PLAN_VIEW = "4",
  PLAIN_TEXT = "5",
  TABLE_MAIN = "6",
  BOREHOLE_COLUMN = "7",
  MISALIGNED_TABLE = "8",
  STATIC_CONE_TEST = "9",
}

export const pageTypeMap = {
  all: '全部',
  [PageType.OTHER_FORM]: '其他成果表',
  [PageType.SECTION_VIEW]: '剖面图',
  [PageType.LEGEND]: '图例',
  [PageType.RICH_TEXT]: '富文本',
  [PageType.PLAN_VIEW]: '平面图',
  [PageType.PLAIN_TEXT]: '纯文本',
  [PageType.TABLE_MAIN]: '表格为主',
  [PageType.BOREHOLE_COLUMN]: '钻孔柱状图',
  [PageType.MISALIGNED_TABLE]: '错位表格成果表',
  [PageType.STATIC_CONE_TEST]: '静力触探成果表'
}

export const PageTypePrefixMap = {
  [PageType.SECTION_VIEW]: "SECTION",
  [PageType.PLAN_VIEW]: "PLANE",
  [PageType.TABLE_MAIN]: "TABLE",
  [PageType.MISALIGNED_TABLE]: "TABLE",
  [PageType.LEGEND]: "LEGEND"
}

const PREFIXES = Array.from(new Set([
  PageTypePrefixMap[PageType.SECTION_VIEW],
  PageTypePrefixMap[PageType.PLAN_VIEW],
  PageTypePrefixMap[PageType.TABLE_MAIN],
  PageTypePrefixMap[PageType.MISALIGNED_TABLE]
]));

export enum DetectLabel {
  SECTION = "section",  // 剖面
  SECTION_NUMBER = "section_number",  // 剖面编号
  SECTION_SCALE = "scale",  // 标尺
  SECTION_DRILL_HOLE = "drill_hole",  // 钻孔编号
  SECTION_SOIL_SAMPLE = "soil_sample",  // 土样
  SECTION_SOIL_SAMPLE_CIRCLE = "soil_sample_circle",  // 土样点
  SECTION_SOIL_SAMPLE_TRIANGLE = "soil_sample_triangle",  // 土样三角形
  SECTION_DEPTH_ELEVATION_PAIR = "depth_elevation_pair",  // 深度-高程对
  PLANE_COORDINATE = "coordinate",  // 坐标
  // PLANE_COORDINATE_WITH_LINE = "coordinate_with_line",  // 带线的坐标
  PLANE_DRILL = "drill",  // 钻孔
  PLANE_DRILL_INFO = "drill_info",  // 钻孔信息
  PLANE_DRILL_INFO_TYPE1 = "drill_info_type1",  // 钻孔信息类型1
  PLANE_DRILL_INFO_TYPE2 = "drill_info_type2",  // 钻孔信息类型2
  PLANE_LITTLE_DRILL = "little_drill",  // 小钻孔
  LEGEND = "legend",  // 图例
  LEGEND_TEXT = "legend_text",  // 图例文本
  TABLE = "table",  // 表格
  TABLE_CELL = "my_table_cell",  // 表格单元格
  TABLE_LAYOUT_TEXT = "text", // 文本
  TABLE_FIGURE_TITLE = "figure_title", // 文档标题
  TABLE_NUMBER = "number", // 页码
  TABLE_FOOTER = "footer", // 页脚
}

export const VisLabelMap = {
  [DetectLabel.SECTION]: '剖面',
  [DetectLabel.SECTION_NUMBER]: '剖面编号',
  [DetectLabel.SECTION_SCALE]: '标尺',
  [DetectLabel.SECTION_DRILL_HOLE]: '钻孔编号',
  [DetectLabel.SECTION_SOIL_SAMPLE]: '土样',
  [DetectLabel.SECTION_SOIL_SAMPLE_CIRCLE]: '圆',
  [DetectLabel.SECTION_SOIL_SAMPLE_TRIANGLE]: '三角',
  [DetectLabel.PLANE_COORDINATE]: '坐标',
  [DetectLabel.SECTION_DEPTH_ELEVATION_PAIR]: '深度-高程对',
  // [DetectLabel.PLANE_COORDINATE_WITH_LINE]: '带线的坐标',
  [DetectLabel.PLANE_DRILL]: '钻孔',
  [DetectLabel.PLANE_DRILL_INFO]: '钻孔信息',
  [DetectLabel.PLANE_DRILL_INFO_TYPE1]: '钻孔信息1',
  [DetectLabel.PLANE_DRILL_INFO_TYPE2]: '钻孔信息2',
  [DetectLabel.PLANE_LITTLE_DRILL]: '小钻孔',
  [DetectLabel.LEGEND]: '图例',
  [DetectLabel.LEGEND_TEXT]: '图例文本',
  [DetectLabel.TABLE]: '表格',
  [DetectLabel.TABLE_CELL]: '表格单元格',
  [DetectLabel.TABLE_LAYOUT_TEXT]: '文本',
  [DetectLabel.TABLE_FIGURE_TITLE]: '文档标题',
  [DetectLabel.TABLE_NUMBER]: '页码',
  [DetectLabel.TABLE_FOOTER]: '页脚',
}

export const DETECT_LABEL_GROUP = groupByPrefixes(DetectLabel, PREFIXES)

export const getVisualLabelGroup = (prefix) => {
  let excludedLabels = []
  let detectLabels = DETECT_LABEL_GROUP[prefix] || []

  // 剖面图
  if (PageTypePrefixMap[PageType.SECTION_VIEW] === prefix) {
    excludedLabels = [
      DetectLabel.SECTION_SOIL_SAMPLE,
    ]
  }
  // 表格
  if (PageTypePrefixMap[PageType.TABLE_MAIN] === prefix) {
    excludedLabels = [
      DetectLabel.TABLE_CELL,
    ]
  }
  detectLabels = detectLabels.filter(label => !excludedLabels.includes(label as DetectLabel))

  return detectLabels
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface HighlightState {
  type: 'bbox' | 'item'; // 高亮类型
  key: string; // 高亮的 key
  animation: boolean; // 是否开启动画
}

export interface ItemInfo {
  id: string;
  title: string;
  image?: {
    imagePath: string;
    boundingBox: BoundingBox;
  };
  expandType?: string; // 'list' or 'grid'
  expanded?: boolean;
  children?: ItemInfo[];
  [key: string]: any; // 用于存储其他属性
}

export class Box {
  id: string
  label: string
  x: number
  y: number
  width: number
  height: number
  isDeleted?: boolean // 是否被删除

  constructor(
    id: string = '', 
    label: string = '', 
    x: number = 0, 
    y: number = 0, 
    width: number = 0, 
    height: number = 0,
    isDeleted: boolean = false
  ) {
    this.id = id
    this.label = label
    this.x = x  
    this.y = y
    this.width = width
    this.height = height
    this.isDeleted = isDeleted
  }
}

export const statusClassMap = {
  0: 'info',
  1: 'warning',
  2: 'primary',
  3: 'success',
  4: 'danger'
}

export const statusMap = {
  0: '代解析',
  1: '待识别',
  2: '识别中',
  3: '识别完成',
  4: '失败'
}
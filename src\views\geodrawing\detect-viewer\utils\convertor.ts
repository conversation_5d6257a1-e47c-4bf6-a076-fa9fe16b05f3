import { CellStyle, MergeCell, ReportConfig, SheetConfig } from '@/components/UniverTable';
import {
  IWorkbookData,
  IWorksheetData,
  BooleanNumber,
  LocaleType,
  IFreeze,
  IRange,
  ICellData,
  IStyleData,
} from '@univerjs/presets'
import { ItemInfo } from '../types';

export function convertSheetConfigToWorksheetData(config: SheetConfig): IWorksheetData {
  const {
    name,
    data,
    dataFormat,
    headers,
    hasHeaders,
    mergeCells,
    freezePanes,
    columnWidths,
    rowHeights,
    startRow,
    startCol,
    styles,
  } = config;

  const cellData: Record<number, Record<number, ICellData>> = {};
  const mergeData: IRange[] = [];

  // 解析 A1 区域
  function parseRange(rng: string): { startRow: number; endRow: number; startColumn: number; endColumn: number } {
    const match = rng.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/);
    if (!match) throw new Error(`Invalid range: ${rng}`);
    const [, c1, r1, c2, r2] = match;
    const colToIndex = (col: string) =>
      col.split('').reduce((prev, ch) => prev * 26 + (ch.charCodeAt(0) - 64), 0) - 1;
    return {
      startRow: parseInt(r1, 10) - 1,
      startColumn: colToIndex(c1),
      endRow: parseInt(r2, 10) - 1,
      endColumn: colToIndex(c2),
    };
  }

  // 处理合并单元格
  mergeCells?.forEach((mc: MergeCell) => {
    const { startRow, endRow, startColumn, endColumn } = parseRange(mc.range);
    mergeData.push({ startRow, endRow, startColumn, endColumn });
    if (mc.value != null || mc.style) {
      const r = startRow, c = startColumn;
      cellData[r] = cellData[r] || {};
      cellData[r][c] = {
        v: mc.value ?? '',
        s: mc.style ? convertStyle(mc.style) : undefined,
      };
    }
  });

  // 组装数据行
  const rows: any[][] = [];
  if (dataFormat === 'dict' && Array.isArray(data) && data.length > 0 && typeof data[0] === 'object') {
    const keys = headers?.length
      ? headers
      : Object.keys(data[0] as Record<string, any>);
    rows.push(keys);
    (data as Record<string, any>[]).forEach(item => {
      rows.push(keys.map(k => item[k]));
    });
  } else if (Array.isArray(data) && Array.isArray(data[0])) {
    if (hasHeaders && headers?.length) {
      rows.push(headers);
    }
    rows.push(...(data as any[][]));
  } else {
    rows.push(Array.isArray(data) ? data : [data]);
  }

  // 填充 cellData
  rows.forEach((row, rIdx) => {
    const r = startRow - 1 + rIdx;
    row.forEach((val, cIdx) => {
      const c = startCol - 1 + cIdx;
      cellData[r] = cellData[r] || {};
      cellData[r][c] = { v: val };
      const key = `${r},${c}`;
      if (styles?.[key]) {
        cellData[r][c].s = convertStyle(styles[key]);
      }
    });
  });

  // 冻结窗格
  const freeze: IFreeze = freezePanes
    ? (() => {
        const [col, row] = freezePanes.split(',').map(str => parseInt(str.trim(), 10) - 1);
        return { startRow: row, startColumn: col, xSplit: col, ySplit: row };
      })()
    : { startRow: -1, startColumn: -1, xSplit: -1, ySplit: -1 };

  const rowCount = Object.keys(cellData).reduce((max, r) => Math.max(max, Number(r)), 0) + 1;
  const allCols = Object.values(cellData)
    .flatMap(row => Object.keys(row).map(c => Number(c)));
  const columnCount = allCols.length ? Math.max(...allCols) + 1 : 0;

  // 样式转换
  function convertStyle(style: CellStyle): IStyleData {
    const s: IStyleData = {};
    if (style.fontName) s.fontFamily = style.fontName;
    if (style.fontSize) s.fontSize = style.fontSize;
    if (style.fontBold !== undefined) s.bold = style.fontBold;
    if (style.fontItalic !== undefined) s.italic = style.fontItalic;
    if (style.fontColor) s.color = style.fontColor;
    if (style.backgroundColor) s.background = style.backgroundColor;
    if (style.horizontalAlign) s.hAlign = style.horizontalAlign;
    if (style.verticalAlign) s.vAlign = style.verticalAlign;
    if (style.numberFormat) s.format = style.numberFormat;
    if (style.border) s.border = { color: style.borderColor || '#000000' };
    return s;
  }

  return {
    id: name,
    name,
    tabColor: '',
    hidden: BooleanNumber.FALSE,
    freeze,
    rowCount,
    columnCount,
    defaultColumnWidth: 100,
    defaultRowHeight: 25,
    mergeData,
    cellData,
    rowData: [],
    columnData: [],
    rowHeader: { width: 40 },
    columnHeader: { height: 20 },
    showGridlines: BooleanNumber.TRUE,
    rightToLeft: BooleanNumber.FALSE,
    defaultStyle: undefined,
  };
}

export function convertSheetConfigsToWorkbookData(...sheetConfigs: SheetConfig[]): IWorkbookData {
  const sheets: Record<string, IWorksheetData> = {}

  sheetConfigs.forEach(sheetConfig => {
    const worksheet = convertSheetConfigToWorksheetData(sheetConfig)
    sheets[worksheet.id] = worksheet
  })

  return {
    id: 'workbook1',
    locale: LocaleType.ZH_CN,
    appVersion: '0.10.2',
    name: 'Workbook 1',
    sheets,
  }
}

/**
 * 将 ReportConfig 转换成 Univer 的 IWorkbookData
 */
export function convertReportConfigToWorkbookData(reportConfig: ReportConfig): IWorkbookData {
  const sheets: Record<string, IWorksheetData> = {}

  reportConfig?.sheets.forEach(sheetConfig => {
    const worksheet = convertSheetConfigToWorksheetData(sheetConfig)
    sheets[worksheet.id] = worksheet
  })

  return {
    id: reportConfig?.outputFilename.replace(/\.[^.]+$/, ''), // 去掉文件扩展名作为 workbook id
    locale: LocaleType.ZH_CN,
    appVersion: '0.10.2',
    name: reportConfig?.outputFilename,
    sheetOrder: reportConfig?.sheets.map(s => s.name),
    sheets,
  }
}

/**
 * 构建嵌套数据的报表配置
 * @param items 嵌套数据
 * @param sheetName 工作表名称
 * @param indentSize 缩进空格数
 */
export function buildNestedSheetConfig(
  items: ItemInfo[],
  sheetName = '嵌套数据报表',
  indentSize = 2
): SheetConfig {
  const rows: any[] = [];
  const dynamicKeys = new Set<string>();
  const titleColName = '类型'

  function traverse(node: ItemInfo, level: number) {
    const { id, title, image, children, expandType, expanded, ...rest } = node;


    // 收集动态字段名
    Object.keys(rest).forEach(k => dynamicKeys.add(k));

    // 缩进 title
    const indent = ' '.repeat(indentSize * level) + '└─';
    const indentedTitle = indent + title;

    // 构造一行
    const row: Record<string, any> = {
      [titleColName]: indentedTitle,
      ...rest
    };
    rows.push(row);

    // 递归子节点
    if (Array.isArray(children)) {
      children.forEach(child => traverse(child, level + 1));
    }
  }

  items.forEach(item => traverse(item, 0));

  // 表头
  const headers = [titleColName, ...Array.from(dynamicKeys)];

  return {
    name: sheetName,
    data: rows,
    headers,
    hasHeaders: true,
    dataFormat: 'dict',
    startRow: 1,
    startCol: 1,
    mergeCells: [],
    columnWidths: {},
    rowHeights: {},
    styles: {}
  };
}
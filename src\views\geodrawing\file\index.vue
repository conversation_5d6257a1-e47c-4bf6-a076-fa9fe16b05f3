<template>
  <div class="app-container">
    <item-card style="margin: 8px 12px 20px;" :project="projectInfo" :labelMap="projectInfoLabel" :col="3" />

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文件名" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button 
          type="primary" 
          plain 
          icon="Upload" 
          @click="handleUpload()"
        >上传图纸</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['geodrawing:file:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['geodrawing:file:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-dropdown split-button type="primary" @click="handleRecog(null, 0)" :disabled="multiple">
          AI识别
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleRecog(null, 1)">仅 OCR 识别</el-dropdown-item>
              <el-dropdown-item @click="handleRecog(null, 2)">仅视觉识别</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          :disabled="multiple || ids.length !== 2"
          @click="handleInvest"
        >开始审查</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          @click="handleReviewPoints"
        >预览审查点</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          @click="handleViewHisProjectReport"
          color="#13c2c2"
        >查看审查报告</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fileList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="图纸文件ID" width="80" align="center" prop="id" />
      <el-table-column label="图纸类型" align="center" prop="fileType">
        <template #default="scope">
          <el-tag
            v-if="scope.row.fileType === 0"
            type="success"
            size="small"
          >总图</el-tag>
          <el-tag
            v-else-if="scope.row.fileType === 1"
            type="warning"
            size="small"
          >地勘报告</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="文件名" align="center" prop="fileName" />
      <el-table-column label="缩略图" align="center" prop="filePath">
        <template #default="scope">
          <div v-if="pageThumbnailUrls[scope.row.id]" class="thumbnail-container">
            <img
              class="thumbnail"
              :key="scope.row.id"
              :src="pageThumbnailUrls[scope.row.id]"
            />
          </div>
          <el-empty v-else :image-size="60" description=" " />
        </template>
      </el-table-column>
      <el-table-column align="center" label="当前阶段" prop="processingStage" />
      <el-table-column align="center" label="进度">
        <template #default="{ row }">
          <el-progress :percentage="Math.floor(row.progress)" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag
            :type="statusClassMap[scope.row.status] || 'info'"
            size="small"
          >{{ statusMap[scope.row.status] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Orange" @click="handleRecog(scope.row)" :disabled="disableRecog">AI识别</el-button>
          <el-button link type="primary" icon="Tickets" @click="handleReview(scope.row)">复核结果</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['geodrawing:file:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['geodrawing:file:remove']">删除</el-button>
          <el-dropdown trigger="click" style="vertical-align: -3px; margin-left: 10px;">
            <el-button link type="primary" icon="ArrowDown">
              更多
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item icon="Files" @click="handleGenReport(scope.row)">生成报告</el-dropdown-item>
                <el-dropdown-item icon="View" @click="handleViewHisReport(scope.row)">查看历史报告</el-dropdown-item>
                <el-dropdown-item icon="Download" @click="handleDownloadLatestReport(scope.row)">下载分析报告（最新）</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList(route.params && route.params.projectId)"
    />

    <!-- 上传图纸文件对话框 -->
    <el-dialog :title="title" v-model="openUpload" width="800px" append-to-body>
      <div class="upload-container">
        <div class="upload-column">
          <h3 class="column-title">总图图纸</h3>
          <el-upload
            class="upload-area"
            drag
            multiple
            action="#"
            :auto-upload="false"
            :file-list="uploadFiles.generalPlan"
            :on-change="handleGeneralPlanChange"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip">支持多个文件</div>
          </el-upload>
        </div>

        <div class="upload-column">
          <h3 class="column-title">勘察报告</h3>
          <el-upload
            class="upload-area"
            drag
            multiple
            action="#"
            :auto-upload="false"
            :file-list="uploadFiles.surveyReport"
            :on-change="handleSurveyReportChange"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip">支持多个文件</div>
          </el-upload>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitUpload">确 定</el-button>
          <el-button @click="cancelUpload">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="File">
import { computed } from "vue"
import { ref, reactive, toRefs } from 'vue'
import { genReport } from '@/api/geodrawing/report'
import { getProject } from "@/api/geodrawing/project"
import { generateOssPresignedUrl } from '@/api/geodrawing/common'
import { listFile, getFile, delFile, investReport, recognizeDrawingPages } from "@/api/geodrawing/file"
import { uploadDrawingFile } from "@/api/geodrawing/file"
import ItemCard from "@/components/DrawingComponents/ItemCard/index.vue"
import wsManager from "@/utils/socket"
import { getRoutePath, GeoRouteType } from '../routeMap'

const { proxy } = getCurrentInstance()

const fileList = ref([])
const open = ref(false)
const openUpload = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const route = useRoute()

const pageThumbnailUrls = ref({})

const statusMap = {
  0: '代解析',
  1: '待识别',
  2: '识别中',
  3: '识别完成',
  4: '失败'
}
const statusClassMap = {
  0: 'info',
  1: 'warning',
  2: 'primary',
  3: 'success',
  4: 'danger'
}

const disableRecog = computed(() => {
  return fileList.value.some(file => [2].includes(file.status) && file.taskId)
})

const uploadFiles = ref({
  projectId: null,
  generalPlan: [],
  surveyReport: [],
})

const projectInfo = reactive({
  id: null,
  name: null,
  description: null,
  status: null,
})

const projectInfoLabel = {
  name: "工程名称",
  description: "工程描述",
  status: "工程状态"
}
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: null,
    fileName: null,
    filePath: null,
    status: null,
  },
  rules: {
    projectId: [
      { required: true, message: "所属工程ID不能为空", trigger: "blur" }
    ],
    fileName: [
      { required: true, message: "文件名不能为空", trigger: "blur" }
    ],
    filePath: [
      { required: true, message: "文件存储路径不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ],
    createBy: [
      { required: true, message: "创建人不能为空", trigger: "blur" }
    ],
    createTime: [
      { required: true, message: "创建时间不能为空", trigger: "blur" }
    ],
    updateBy: [
      { required: true, message: "修改人不能为空", trigger: "blur" }
    ],
    updateTime: [
      { required: true, message: "修改时间不能为空", trigger: "blur" }
    ],
    remark: [
      { required: true, message: "备注不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

function getProjectInfo(proejctId) {
  getProject(proejctId).then(response => {
    projectInfo.id = response.data.id
    projectInfo.name = response.data.name
    projectInfo.description = response.data.description
    projectInfo.status = response.data.status
    getList(proejctId)
  }).catch(() => {
    proxy.$modal.msgError("获取工程信息失败")
  })
}

/** 查询图纸文件列表 */
function getList(projectId) {
  projectId = projectId || projectInfo.id
  loading.value = true
  return new Promise((resolve) => {
    listFile({
      ...queryParams.value,
      projectId,
    }).then(response => {
      fileList.value = response.rows
      total.value = response.total
      loading.value = false
      for (const file of fileList.value) {
        loadPageThumbnailUrl(file.id, file.thumbnailPath)
      }
      resolve(response.rows)
      return response.rows
    })
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 取消上传按钮
function cancelUpload() {
  openUpload.value = false
  resetUploadFiles()
  proxy.$refs["uploadRef"].clearFiles() // 清除上传组件的文件列表
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    projectId: null,
    fileName: null,
    filePath: null,
    status: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
  proxy.resetForm("fileRef")
}

function resetUploadFiles() {
  uploadFiles.value = {
    projectId: null,
    generalPlan: [],
    surveyReport: []
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

function handleInvest() {
  const _ids = ids.value.length > 0 ? ids.value : null
  // 校验选择数量和类型
  if (!_ids) {
    proxy.$modal.msgError("请先选择要审查的图纸文件")
    return
  }
  if (_ids.length != 2) {
    proxy.$modal.msgError("请选择两份图纸文件进行审查（总图和地勘报告各一份）")
    return
  }
  if (!(_ids.some(id => {
    const file = fileList.value.find(t => t.id === id)
    return file && file.fileType === 0
  }) && _ids.some(id => {
    const file = fileList.value.find(t => t.id === id)
    return file && file.fileType === 1
  }))) {
    proxy.$modal.msgError("请选择一份总图和一份地勘报告进行审查")
    return
  }
  // 提交审查任务
  investReport(_ids[0], _ids[1],projectInfo.id).then(resp => {
    proxy.$modal.msgSuccess("审查任务已提交，请等待审查完成后查看结果")
    getList(projectInfo.id)
  }).catch(error => {
    proxy.$modal.msgError("审查任务提交失败：" + error.message)
  })
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

// 懒加载缩略图
async function loadPageThumbnailUrl(id, filePath) {
  if (!filePath) return;
  if (!pageThumbnailUrls.value[id]) {
    const resp = await generateOssPresignedUrl(filePath);
    pageThumbnailUrls.value[id] = resp.data;
  }
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加图纸文件"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getFile(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改图纸文件"
  })
}

/** 预览审查点 */
function handleReviewPoints() {
  const _ids = ids.value.length > 0 ? ids.value : null
  if (!_ids) {
    proxy.$modal.msgError("请先选择要预览的图纸文件")
    return
  }
}

/** 生成分析报告 */
async function handleGenReport(row) {
  const _id = row.id || ids.value
  if (!_id) {
    proxy.$modal.msgError("请先选择要生成报告的图纸文件")
    return
  }
  proxy.$modal.confirm('是否确认生成分析报告？').then(function() {
    return genReport(_id)
  }).then(resp => {
    proxy.$modal.msgSuccess("正在后台生成报告，请稍后。")
  }).catch(() => {})
}

/** 查看历史报告 */
function handleViewHisReport(row) {
  const _id = row.id || ids.value
  if (!_id) {
    proxy.$modal.msgError("请先选择要查看历史报告的图纸文件")
    return
  }
  proxy.$router.push({ path: getRoutePath(GeoRouteType.GeoReport), query: { drawingId: _id } })
}

function handleViewHisProjectReport(row) {
  const projectId = projectInfo.id
  if (!projectId) {
    proxy.$modal.msgError("请先选择要查看历史报告的工程")
    return
  }
  proxy.$router.push({ path: getRoutePath(GeoRouteType.GeoReviewReport), query: { projectId: projectId } })
}

/** 下载最新分析报告 */
function handleDownloadLatestReport(row) {
  const _id = row.id || ids.value
  if (!_id) {
    proxy.$modal.msgError("请先选择要下载的图纸文件")
    return
  }
  proxy.download('/geodrawing/report/download', {
    drawingId: _id
  }, `report_${_id}.xlsx`)
}

/** 预览识别结果 */
function handleReview(row) {
  const _id = row.id || ids.value
  proxy.$router.push({ path: getRoutePath(GeoRouteType.GeoViewer, _id) })
}

/** 上传图纸按钮操作 */
function handleUpload() {
  resetUploadFiles()
  uploadFiles.value.projectId = projectInfo.id
  openUpload.value = true
  title.value = "上传图纸文件"
}

/** 上传图纸文件变化处理 */
function handleGeneralPlanChange(file, fileList) {
  uploadFiles.value.generalPlan = fileList
}

/** 勘察报告上传图纸文件变化处理 */
function handleSurveyReportChange(file, fileList) {
  uploadFiles.value.surveyReport = fileList
}

/** 上传预处理 */
function beforeUpload() {
  // 检查总图图纸
  if (uploadFiles.value.generalPlan.length === 0) {
    proxy.$modal.msgError("请上传总图图纸!")
    return false
  }
  uploadFiles.value.generalPlan.forEach(file => {
    if (!checkFile(file)) {
      return false
    }
  })
  // 检查勘察报告
  if (uploadFiles.value.surveyReport.length === 0) {
    proxy.$modal.msgError("请上传勘察报告!")
    return false
  }
  uploadFiles.value.surveyReport.forEach(file => {
    if (!checkFile(file)) {
      return false
    }
  })
  return true
}

/** 检查文件类型 */
function checkFile(file) {
  if (file.raw.type.indexOf("application/pdf") == -1) {
    proxy.$modal.msgError("文件格式错误，请上传PDF类型的文件。")
    return false
  }
  return true
}

/** 上传图纸 */
function submitUpload() {
  if (!beforeUpload()) return

  proxy.$modal.confirm('是否确认上传图纸文件？').then(() => {
    uploadFiles.value.projectId = uploadFiles.value.projectId || form.value.id
    if (!uploadFiles.value.projectId) {
      proxy.$modal.msgError("请先选择或创建一个工程。")
      return
    }
    // 构建上传数据

    let formData = new FormData()
    formData.append("projectId", uploadFiles.value.projectId)
    for (let file of uploadFiles.value.generalPlan) {
      formData.append("generalPlans", file.raw)
    }
    for (let file of uploadFiles.value.surveyReport) {
      formData.append("surveyReports", file.raw)
    }
    uploadDrawingFile(formData).then(resp => {
      if (resp.code !== 200) {
        proxy.$modal.msgError("上传失败：" + resp.msg)
        return
      } else {
        proxy.$modal.msgSuccess(resp.msg)
        getList(uploadFiles.value.projectId).then(() => {        
          listenToTaskProgress(resp.data.socketIds)
        })
      }
      openUpload.value = false
    })
    .catch(error => {
      proxy.$modal.msgError("上传失败：" + error.message)
    })
  })
}

/**
 * 监听任务进度
 * @param {Array} socketIds - socket ID 数组
 */
function listenToTaskProgress(socketIds) {
  if (!Array.isArray(socketIds) || socketIds.length === 0) {
    console.warn('socketIds 必须是非空数组');
    return;
  }

  console.log("开始监听任务进度，任务ID:", socketIds);

  for (const socketId of socketIds) {
    const url = `/ws/drawingFileProcess?socketId=${socketId}`;
    
    wsManager.connect(socketId, url, {
      onMessage: (data, socketId) => {
        // 更新任务数据
        const rowData = fileList.value.find(t => t.socketId === socketId);
        if (rowData) {
          rowData.processingStage = data.phase ?? rowData.processingStage;
          rowData.progress = data.progress ?? rowData.progress;
          rowData.status = data.status ?? rowData.status;
        } else {
          console.warn(`未找到对应 socketId=${socketId} 的任务数据`);
        }
        
        if (data?.finished) {
          proxy.$modal.msgSuccess("任务已完成");
          wsManager.disconnect(socketId);
          getList();
          return;
        }
      },
      
      onError: (error, socketId) => {
        proxy.$modal.msgError(`WebSocket 连接错误（socketId=${socketId}）: ${error.message || '未知错误'}`);
      }
    });
  }
}

// 页面卸载时清理所有连接
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    wsManager.disconnectAll();
  });
}

// 识别前校验
function beforeRecog(ids) {
  const rowData = fileList.value.find(t => t.status === 2 && t.socketId);
  if (rowData) {
    proxy.$modal.msgError("当前有任务正在识别中，请稍后再试。")
    return false
  }
  return true
}

// 执行视觉识别任务
function handleRecog(row, type) {
  const _id = [].concat(row?.id || ids.value || [])
  if (!_id || _id.length === 0) {
    proxy.$modal.msgError("请选择要识别的图纸文件")
    return
  }
  if (!beforeRecog(_id)) return
  const data = _id.map(id => ({ drawingId: id, taskType: type || 0 }))
  recognizeDrawingPages(data).then(resp => {
    if (resp.code !== 200) {
      proxy.$modal.msgError("识别失败：" + resp.msg)
      return
    }
    proxy.$modal.msgSuccess("识别任务已提交，请等待识别完成后查看结果")
    getList(projectInfo.id).then(() => {
      listenToTaskProgress(resp.data)
    })
  }).catch(error => {
    proxy.$modal.msgError("识别失败：" + error.message)
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除图纸文件编号为"' + _ids + '"的数据项？').then(function() {
    return delFile(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('geodrawing/file/export', {
    ...queryParams.value
  }, `file_${new Date().getTime()}.xlsx`)
}

getProjectInfo(route.params && route.params.projectId)
getList(route.params && route.params.projectId)
</script>

<style scoped>
.upload-container {
  display: flex;
  gap: 20px;
  padding: 10px 20px;
}

.upload-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.column-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.upload-area {
  /* border: 2px dashed #dcdfe6; */
  border-radius: 6px;
  padding: 20px 10px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}

.thumbnail-container {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  border: 1px solid #ddd;
}
.thumbnail {
  width: 100%;
  max-height: 80px;
  object-fit: cover;
}
.el-empty {
  max-height: 80px;
}
.el-dropdown {
  vertical-align: top;
}
.el-dropdown + .el-dropdown {
  margin-left: 15px;
}
.el-icon-arrow-down {
  font-size: 12px;
}
</style>

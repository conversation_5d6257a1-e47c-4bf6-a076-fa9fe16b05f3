<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属图纸ID" prop="drawingId">
        <el-input
          v-model="queryParams.drawingId"
          placeholder="请输入所属图纸ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第几页" prop="pageNumber">
        <el-input
          v-model="queryParams.pageNumber"
          placeholder="请输入第几页"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="推荐使用的AI模型名称" prop="aiModel">
        <el-input
          v-model="queryParams.aiModel"
          placeholder="请输入推荐使用的AI模型名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['geodrawing:page:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['geodrawing:page:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['geodrawing:page:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['geodrawing:page:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="页面ID" align="center" prop="id" />
      <el-table-column label="所属图纸ID" align="center" prop="drawingId" />
      <el-table-column label="第几页" align="center" prop="pageNumber" />
      <el-table-column label="页面图像路径" align="center" prop="imagePath" />
      <el-table-column label="结构化状态" align="center" prop="structureStatus" />
      <el-table-column label="推荐使用的AI模型名称" align="center" prop="aiModel" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['geodrawing:page:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['geodrawing:page:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改图纸页面对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="pageRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="所属图纸ID" prop="drawingId">
          <el-input v-model="form.drawingId" placeholder="请输入所属图纸ID" />
        </el-form-item>
        <el-form-item label="第几页" prop="pageNumber">
          <el-input v-model="form.pageNumber" placeholder="请输入第几页" />
        </el-form-item>
        <el-form-item label="页面图像路径" prop="imagePath">
          <el-input v-model="form.imagePath" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="推荐使用的AI模型名称" prop="aiModel">
          <el-input v-model="form.aiModel" placeholder="请输入推荐使用的AI模型名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Page">
import { listPage, getPage, delPage, addPage, updatePage } from "@/api/geodrawing/page"

const { proxy } = getCurrentInstance()

const pageList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    drawingId: null,
    pageNumber: null,
    imagePath: null,
    structureStatus: null,
    aiModel: null,
  },
  rules: {
    drawingId: [
      { required: true, message: "所属图纸ID不能为空", trigger: "blur" }
    ],
    pageNumber: [
      { required: true, message: "第几页不能为空", trigger: "blur" }
    ],
    imagePath: [
      { required: true, message: "页面图像路径不能为空", trigger: "blur" }
    ],
    structureStatus: [
      { required: true, message: "结构化状态不能为空", trigger: "change" }
    ],
    createBy: [
      { required: true, message: "创建人不能为空", trigger: "blur" }
    ],
    createTime: [
      { required: true, message: "创建时间不能为空", trigger: "blur" }
    ],
    updateBy: [
      { required: true, message: "修改人不能为空", trigger: "blur" }
    ],
    updateTime: [
      { required: true, message: "修改时间不能为空", trigger: "blur" }
    ],
    remark: [
      { required: true, message: "备注不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询图纸页面列表 */
function getList() {
  loading.value = true
  listPage(queryParams.value).then(response => {
    pageList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    drawingId: null,
    pageNumber: null,
    imagePath: null,
    structureStatus: null,
    aiModel: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
  proxy.resetForm("pageRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加图纸页面"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getPage(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改图纸页面"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["pageRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updatePage(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addPage(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除图纸页面编号为"' + _ids + '"的数据项？').then(function() {
    return delPage(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('geodrawing/page/export', {
    ...queryParams.value
  }, `page_${new Date().getTime()}.xlsx`)
}

getList()
</script>

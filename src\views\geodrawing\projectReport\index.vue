<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="所属审核记录ID" prop="auditId">
        <el-input
          v-model="queryParams.auditId"
          placeholder="请输入所属审核记录ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报告生成时间" prop="generateTime">
        <el-date-picker clearable
          v-model="queryParams.generateTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择报告生成时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleGenReport"
          v-hasPermi="['geodrawing:report:add']"
        >生成报告</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['geodrawing:report:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['geodrawing:report:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reportList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报告主键ID" align="center" prop="id" />
      <el-table-column label="所属审核记录ID" align="center" prop="auditId" />
      <el-table-column label="报告文件路径" align="center" prop="reportPath" />
      <el-table-column label="在线预览HTML路径" align="center" prop="htmlPath" />
      <el-table-column label="报告生成时间" align="center" prop="generateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.generateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="{ row }">
          <el-tag
            v-if="row.status === 2"
            type="success"
            size="small"
          >已生成</el-tag>
          <el-tag
            v-else-if="row.status === 1"
            type="info"
            size="small"
          >生成中</el-tag>
          <el-tag
            v-else
            type="danger"
            size="small"
          >生成失败</el-tag>
          
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Download" @click="handleDownload(scope.row)" v-hasPermi="['geodrawing:report:download']">下载</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['geodrawing:report:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Report">
import { listReport, listProjectReport, getReport, delReport, addReport, updateReport, genReport, repStatus } from "@/api/geodrawing/report"
import { reactive } from "vue"

const { proxy } = getCurrentInstance()

const reportList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectId: null,
    auditId: null,
    reportPath: null,
    htmlPath: null,
    generateTime: null
  },
  rules: {
    auditId: [
      { required: true, message: "所属审核记录ID不能为空", trigger: "blur" }
    ],
    reportPath: [
      { required: true, message: "报告文件路径不能为空", trigger: "blur" }
    ],
    generateTime: [
      { required: true, message: "报告生成时间不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 轮询报告生成状态 */
let pollTimer = null

function pollReportStatus() {
  // 清除旧的轮询
  if (pollTimer) {
    clearTimeout(pollTimer);
    pollTimer = null;
  }

  const doPoll = async () => {
    const pendingReports = reportList.value.filter(r => r.status === 1);
    if (pendingReports.length === 0) {
      console.log('轮询结束');
      return; // 结束
    }

    for (const report of pendingReports) {
      try {
        const resp = await repStatus(report.id);
        if (resp.data.status === 'completed') {
          getList();
        } else if (resp.data.status === 'failed') {
          report.status = 3; // 失败
        }
      } catch (err) {
        console.error('获取状态失败', err);
      }
    }

    // 下次轮询
    pollTimer = setTimeout(doPoll, 1000);
  };

  doPoll();
}

/** 组件销毁时清除轮询定时器 */
onBeforeUnmount(() => {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
})

/** 查询审核报告列表 */
function getList() {
  loading.value = true
  queryParams.value.projectId = proxy.$route.query.projectId || null
  listProjectReport(queryParams.value).then(response => {
    reportList.value = response.rows
    total.value = response.total
    loading.value = false
    if (response.rows.length > 0) {
      pollReportStatus() // 开始轮询报告状态
    }
  })
}

/** 生成识别报告按钮操作 */
function handleGenReport() {
  proxy.$modal.confirm('是否确认生成分析报告？').then(function() {
    return genReport(null, proxy.$route.query.projectId)
  }).then(resp => {
    getList()
    proxy.$modal.msgSuccess("正在后台生成报告，请稍后。")
  }).catch(() => {})
}

/** 下载报告按钮操作 */
function handleDownload(row) {
  const _id = row.id || ids.value
  if (!_id) {
    proxy.$modal.msgError("请先选择要下载的报告")
    return
  }
  if (row.status !== 2) {
    proxy.$modal.msgError(`报告${_id}未生成，无法下载`)
    return
  }
  if (row.reportPath === null) {
    proxy.$modal.msgError(`报告${_id}路径异常，无法下载`)
    return
  }
  proxy.download('/geodrawing/report/download', {
    reportId: _id
  }, `report_${_id}.xlsx`)
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除审核报告编号为"' + _ids + '"的数据项？').then(function() {
    return delReport(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('geodrawing/report/export', {
    ...queryParams.value
  }, `report_${new Date().getTime()}.xlsx`)
}

getList()
</script>

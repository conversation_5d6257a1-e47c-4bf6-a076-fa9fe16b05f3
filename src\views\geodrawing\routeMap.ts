export enum GeoRouteType {
  GeoFile = 'geoFile',
  GeoViewer = 'geoViewer',
  GeoReport = 'geoReport',
  GeoReviewReport = 'geoReviewReport',
}

const GEO_INNER_ROUTE_MAP = {
  [GeoRouteType.GeoFile]: '/geodrawing/file/index',
  [GeoRouteType.GeoViewer]: '/geodrawing/detect-viewer/index',
  [GeoRouteType.GeoReport]: '/geodrawing/report',
  [GeoRouteType.GeoReviewReport]: '/geodrawing/projectReport',
}

const GEO_OUTER_ROUTE_MAP = {
  [GeoRouteType.GeoFile]: '/geological/file',
  [GeoRouteType.GeoViewer]: '/geological/detect-viewer',
  [GeoRouteType.GeoReport]: '/geological/report',
  [GeoRouteType.GeoReviewReport]: '/geological/projectReport',
}

export const getRoutePath = (type: GeoRouteType, id: number) => {
  // const route_map = GEO_INNER_ROUTE_MAP
  const route_map = GEO_OUTER_ROUTE_MAP

  if (id) return `${route_map[type]}/${id}`
  return route_map[type] || ''
}